# HTML转Word工具

## 简介

将试题HTML文件转换为Word文档的工具，基于`html-to-docx`库实现。

## 快速使用

### 1. 基本转换

```typescript
import { generateWordFromHtml } from './utils/word-export/html-to-word-converter';

// 最简单的用法
await generateWordFromHtml(htmlContent, 'output.docx');
```

### 2. 从文件转换

```typescript
import { HtmlToWordConverter } from './utils/word-export/html-to-word-converter';

const converter = new HtmlToWordConverter();
await converter.convertHtmlFileToWord('input.html', 'output.docx');
```

## 测试

```bash
# 运行测试
node --require ts-node/register src/example/html-to-word-test.ts
```

**测试文件：**
- 输入：`testFiles/input-html-sample.html`
- 输出：`testFiles/output-word-result-new.docx`

打开生成的Word文档查看转换效果。

## 配置选项

```typescript
const options = {
  font: 'Microsoft YaHei',      // 字体
  fontSize: 12,                 // 字体大小
  preprocessHtml: true          // 启用预处理（推荐）
};

await generateWordFromHtml(htmlContent, 'output.docx', options);
```

## 功能特点

- ✅ 试题序号自动格式化
- ✅ 选项自动缩进
- ✅ 表格边框自动添加
- ✅ 着重号转换为粗体下划线
- ✅ 支持中文字体
- ✅ 自动清理无用样式

## 依赖

- `html-to-docx`: HTML转Word核心库
- `jsdom`: HTML预处理

## 文件结构

```
word-export/
├── html-to-word-converter.ts  # 主要转换器
├── README.md                   # 本文档
└── README-refactor.md         # 详细重构说明
```
