import {
  HtmlToWordConverter,
  convertHtmlToWord,
  generateWordFromHtml,
  HtmlToWordOptions,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 统一的输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');
const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');

async function testMethod1_ConverterClass() {
  console.log('=== 测试方法1: 转换器类方法 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 检查输入文件是否存在
    if (!fs.existsSync(inputHtmlPath)) {
      throw new Error(`输入HTML文件不存在: ${inputHtmlPath}`);
    }

    const outputWordPath = path.join(testFilesDir, 'output-word-result1.docx');
    console.log(`输入文件: ${inputHtmlPath}`);
    console.log(`输出文件: ${outputWordPath}`);

    // 读取HTML内容
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`HTML文件大小: ${htmlContent.length} 字符\n`);

    // 配置转换选项
    const options: HtmlToWordOptions = {
      font: 'Microsoft YaHei',
      fontSize: 12,
      orientation: 'portrait',
      margins: {
        top: 1440, // 1英寸 = 1440 twips
        right: 1440,
        bottom: 1440,
        left: 1440,
      },
      table: {
        row: {
          cantSplit: true,
        },
      },
      preprocessHtml: true,
    };

    console.log('开始转换...');

    // 执行转换
    const startTime = Date.now();
    await converter.convertHtmlFileToWord(
      inputHtmlPath,
      outputWordPath,
      options
    );
    const endTime = Date.now();

    console.log(`✓ 方法1转换完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ 输出文件: ${outputWordPath}`);

    // 检查输出文件
    const stats = await fs.promises.stat(outputWordPath);
    console.log(`✓ 输出文件大小: ${Math.round(stats.size / 1024)}KB`);
  } catch (error) {
    console.error('❌ 方法1转换失败:', error);
    throw error;
  }
}

async function testMethod2_ConvenienceFunction() {
  console.log('\n=== 测试方法2: 便捷函数方法 ===\n');

  try {
    // 读取HTML内容
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`读取HTML文件: ${inputHtmlPath}`);
    console.log(`HTML文件大小: ${htmlContent.length} 字符`);

    const outputWordPath = path.join(testFilesDir, 'output-word-result2.docx');
    console.log(`输出文件: ${outputWordPath}`);

    console.log('开始转换...');

    const startTime = Date.now();
    await generateWordFromHtml(htmlContent, outputWordPath, {
      font: 'Microsoft YaHei',
      fontSize: 14,
      preprocessHtml: true,
    });
    const endTime = Date.now();

    console.log(`✓ 方法2转换完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ 输出文件: ${outputWordPath}`);

    // 检查文件大小
    const stats = await fs.promises.stat(outputWordPath);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);
  } catch (error) {
    console.error('❌ 方法2转换失败:', error);
    throw error;
  }
}

async function testMethod3_BufferConversion() {
  console.log('\n=== 测试方法3: Buffer转换方法 ===\n');

  try {
    // 读取HTML内容
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`读取HTML文件: ${inputHtmlPath}`);
    console.log(`HTML文件大小: ${htmlContent.length} 字符`);

    console.log('开始Buffer转换...');

    const startTime = Date.now();
    const wordBuffer = await convertHtmlToWord(htmlContent, {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: true,
    });
    const endTime = Date.now();

    console.log(`✓ 方法3Buffer转换完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ Buffer大小: ${Math.round(wordBuffer.length / 1024)}KB`);

    const outputWordPath = path.join(testFilesDir, 'output-word-result1.docx');

    // 保存Buffer到文件进行验证
    await fs.promises.writeFile(outputWordPath, wordBuffer);
    console.log(`✓ Buffer已保存到: ${outputWordPath}`);
  } catch (error) {
    console.error('❌ 方法3Buffer转换失败:', error);
    throw error;
  }
}

// 运行所有测试
async function runAllTests() {
  try {
    console.log('=== HTML转Word转换测试 ===');
    console.log('所有方法都使用相同的输入和输出文件，便于对比效果\n');

    await testMethod1_ConverterClass();
    await testMethod2_ConvenienceFunction();
    await testMethod3_BufferConversion();

    console.log('\n=== 所有测试完成 ===');
    console.log('请打开以下文件查看转换效果:');
    console.log(`- 输入文件: ${inputHtmlPath}`);
    console.log(`- 输出文件: ${outputWordPath}`);
  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  testMethod1_ConverterClass,
  testMethod2_ConvenienceFunction,
  testMethod3_BufferConversion,
  runAllTests,
};
