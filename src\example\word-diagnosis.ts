import {
  HtmlToWordConverter,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');

/**
 * Word转换诊断工具
 * 输出详细的转换过程信息
 */
async function diagnoseWordConversion() {
  console.log('=== Word转换诊断 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 读取原始HTML
    const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');
    const originalHtml = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    
    console.log('📊 原始HTML分析:');
    console.log(`- 总长度: ${originalHtml.length.toLocaleString()} 字符`);
    console.log(`- 包含"新鲜感": ${(originalHtml.match(/新鲜感/g) || []).length} 次`);
    console.log(`- 包含"下面对有": ${(originalHtml.match(/下面对有/g) || []).length} 次`);
    console.log(`- 包含"A．": ${(originalHtml.match(/A\./g) || []).length} 次`);
    console.log(`- 包含table标签: ${(originalHtml.match(/<table/g) || []).length} 次`);

    // 测试预处理
    console.log('\n🔧 预处理分析:');
    const preprocessedHtml = (converter as any).preprocessHtml(originalHtml);
    console.log(`- 预处理后长度: ${preprocessedHtml.length.toLocaleString()} 字符`);
    console.log(`- 保留比例: ${Math.round((preprocessedHtml.length / originalHtml.length) * 100)}%`);
    console.log(`- 包含"新鲜感": ${(preprocessedHtml.match(/新鲜感/g) || []).length} 次`);
    console.log(`- 包含"下面对有": ${(preprocessedHtml.match(/下面对有/g) || []).length} 次`);
    console.log(`- 包含"A．": ${(preprocessedHtml.match(/A\./g) || []).length} 次`);
    console.log(`- 包含table标签: ${(preprocessedHtml.match(/<table/g) || []).length} 次`);

    // 保存诊断HTML
    const diagnosisHtmlPath = path.join(testFilesDir, 'diagnosis-output.html');
    const diagnosisHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Word转换诊断</title>
  <style>
    body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .stats { background: #f0f8ff; }
    .content { background: #f9f9f9; max-height: 400px; overflow-y: auto; }
    pre { white-space: pre-wrap; font-size: 12px; }
    .highlight { background: yellow; }
  </style>
</head>
<body>
  <h1>Word转换诊断报告</h1>
  
  <div class="section stats">
    <h2>📊 统计信息</h2>
    <p><strong>原始HTML:</strong> ${originalHtml.length.toLocaleString()} 字符</p>
    <p><strong>预处理后:</strong> ${preprocessedHtml.length.toLocaleString()} 字符</p>
    <p><strong>保留比例:</strong> ${Math.round((preprocessedHtml.length / originalHtml.length) * 100)}%</p>
  </div>

  <div class="section content">
    <h2>🔍 预处理后内容（前2000字符）</h2>
    <pre>${preprocessedHtml.substring(0, 2000).replace(/新鲜感/g, '<span class="highlight">新鲜感</span>')}</pre>
  </div>

  <div class="section content">
    <h2>📝 完整预处理内容</h2>
    <div style="max-height: 600px; overflow-y: auto;">
      ${preprocessedHtml}
    </div>
  </div>
</body>
</html>`;

    fs.writeFileSync(diagnosisHtmlPath, diagnosisHtml, 'utf-8');
    console.log(`✓ 诊断HTML已保存: ${diagnosisHtmlPath}`);

    // 测试最小化HTML转换
    console.log('\n🧪 最小化测试:');
    const minimalHtml = `
      <h1>测试标题</h1>
      <p>1. 这是第一题，包含<span style="text-emphasis: filled dot;">新鲜感</span>这个词。</p>
      <p style="margin-left: 20px;">A. 选项A的内容</p>
      <p style="margin-left: 20px;">B. 选项B的内容</p>
    `;

    const minimalOutputPath = path.join(testFilesDir, 'minimal-test.docx');
    await converter.generateWordFile(minimalHtml, minimalOutputPath, {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: false
    });

    const minimalStats = await fs.promises.stat(minimalOutputPath);
    console.log(`✓ 最小化测试完成: ${minimalOutputPath}`);
    console.log(`✓ 文件大小: ${Math.round(minimalStats.size / 1024)}KB`);

    console.log('\n=== 诊断完成 ===');
    console.log('📋 检查步骤:');
    console.log('1. 打开 diagnosis-output.html 查看预处理详情');
    console.log('2. 打开 minimal-test.docx 查看最小化测试效果');
    console.log('3. 对比各个Word文档的显示效果');
    console.log('4. 确定问题出现在哪个环节');

  } catch (error) {
    console.error('❌ 诊断失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await diagnoseWordConversion();
  } catch (error) {
    console.error('\n❌ 诊断失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { diagnoseWordConversion, main };
