import {
  HtmlToWordConverter,
  HtmlToWordOptions,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');
const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');
const outputWordPath = path.join(testFilesDir, 'output-word-result-new.docx');

/**
 * HTML转Word转换器 - 功能最全的实现
 * 支持详细配置和后期微调
 */
async function convertHtmlToWord() {
  console.log('=== HTML转Word转换器 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 检查输入文件
    if (!fs.existsSync(inputHtmlPath)) {
      throw new Error(`输入HTML文件不存在: ${inputHtmlPath}`);
    }

    console.log(`输入文件: ${inputHtmlPath}`);
    console.log(`输出文件: ${outputWordPath}`);

    // 读取HTML内容
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`HTML文件大小: ${htmlContent.length} 字符\n`);

    // 详细的转换配置 - 支持后期微调
    const options: HtmlToWordOptions = {
      // 字体设置
      font: 'Microsoft YaHei', // 中文字体
      fontSize: 12, // 字体大小

      // 页面设置
      orientation: 'portrait', // 纵向
      margins: {
        top: 1440, // 上边距 1英寸
        right: 1440, // 右边距 1英寸
        bottom: 1440, // 下边距 1英寸
        left: 1440, // 左边距 1英寸
      },

      // 表格设置
      table: {
        row: {
          cantSplit: true, // 表格行不跨页分割
        },
      },

      // 预处理设置
      preprocessHtml: true, // 启用预处理，清理有问题的属性
    };

    console.log('转换配置:');
    console.log(`- 字体: ${options.font} ${options.fontSize}pt`);
    console.log(`- 页面: ${options.orientation}`);
    console.log(
      `- 边距: ${options.margins?.top}twips (${
        (options.margins?.top || 0) / 1440
      }英寸)`
    );
    console.log(`- 预处理: ${options.preprocessHtml ? '启用' : '禁用'}`);
    console.log('');

    console.log('开始转换...');
    const startTime = Date.now();

    // 执行转换
    await converter.convertHtmlFileToWord(
      inputHtmlPath,
      outputWordPath,
      options
    );

    const endTime = Date.now();
    console.log(`✓ 转换完成！耗时: ${endTime - startTime}ms`);

    // 检查输出文件
    const stats = await fs.promises.stat(outputWordPath);
    console.log(`✓ 输出文件: ${outputWordPath}`);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);

    console.log('\n=== 转换完成 ===');
    console.log('📄 生成的文件:');
    console.log(`   Word文档: ${outputWordPath}`);
    console.log(
      `   HTML预览: ${path.join(testFilesDir, 'processed-html-preview.html')}`
    );
    console.log('');
    console.log('📋 查看步骤:');
    console.log('1. 打开Word文档查看最终转换效果');
    console.log('2. 打开HTML预览文件查看预处理效果');
    console.log('3. 如果效果不理想，参考 conversion-guide.md 调整配置');
  } catch (error) {
    console.error('❌ 转换失败:', error);
    throw error;
  }
}

/**
 * 微调配置示例
 * 根据转换效果调整这些参数
 */
async function convertWithCustomSettings() {
  console.log('\n=== 自定义配置转换示例 ===\n');

  const converter = new HtmlToWordConverter();
  const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');

  // 示例：更大字体，更小边距
  const customOptions: HtmlToWordOptions = {
    font: 'SimSun', // 宋体
    fontSize: 14, // 更大字体
    orientation: 'portrait',
    margins: {
      top: 720, // 0.5英寸边距
      right: 720,
      bottom: 720,
      left: 720,
    },
    table: {
      row: { cantSplit: true },
    },
    preprocessHtml: true,
  };

  console.log('自定义配置:');
  console.log(`- 字体: ${customOptions.font} ${customOptions.fontSize}pt`);
  console.log(
    `- 边距: ${customOptions.margins?.top}twips (${
      (customOptions.margins?.top || 0) / 1440
    }英寸)`
  );

  const customOutputPath = path.join(
    testFilesDir,
    'output-word-result-custom.docx'
  );

  try {
    console.log('\n开始自定义转换...');
    const startTime = Date.now();

    await converter.generateWordFile(
      htmlContent,
      customOutputPath,
      customOptions
    );

    const endTime = Date.now();
    console.log(`✓ 自定义转换完成！耗时: ${endTime - startTime}ms`);

    const stats = await fs.promises.stat(customOutputPath);
    console.log(`✓ 自定义文件: ${customOutputPath}`);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);
  } catch (error) {
    console.error('❌ 自定义转换失败:', error);
  }
}

/**
 * 主函数 - 运行转换测试
 */
async function main() {
  try {
    // 标准转换
    await convertHtmlToWord();

    // 自定义配置转换（可选）
    // await convertWithCustomSettings();
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { convertHtmlToWord, convertWithCustomSettings, main };
