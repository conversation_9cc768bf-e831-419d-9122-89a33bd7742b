import {
  HtmlToWordConverter,
  convertHtmlToWord,
  generateWordFromHtml,
  HtmlToWordOptions,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

async function testHtmlToWordConversion() {
  console.log('=== 测试HTML转Word功能 ===\n');

  const converter = new HtmlToWordConverter();
  const testFilesDir = path.join(__dirname, '../../testFiles');

  // 输入和输出文件路径
  const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');
  const outputWordPath = path.join(testFilesDir, 'output-word-result.docx');

  try {
    // 检查输入文件是否存在
    if (!fs.existsSync(inputHtmlPath)) {
      throw new Error(`输入HTML文件不存在: ${inputHtmlPath}`);
    }

    console.log(`输入文件: ${inputHtmlPath}`);
    console.log(`输出文件: ${outputWordPath}`);

    // 读取HTML内容
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`HTML文件大小: ${htmlContent.length} 字符\n`);

    // 配置转换选项
    const options: HtmlToWordOptions = {
      font: 'Microsoft YaHei',
      fontSize: 12,
      orientation: 'portrait',
      margins: {
        top: 1440, // 1英寸 = 1440 twips
        right: 1440,
        bottom: 1440,
        left: 1440,
      },
      table: {
        row: {
          cantSplit: true,
        },
      },
      preprocessHtml: true,
    };

    console.log('转换选项:', JSON.stringify(options, null, 2));
    console.log('\n开始转换...');

    // 执行转换
    const startTime = Date.now();
    await converter.convertHtmlFileToWord(
      inputHtmlPath,
      outputWordPath,
      options
    );
    const endTime = Date.now();

    console.log(`✓ 转换完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ 输出文件: ${outputWordPath}`);

    // 检查输出文件
    const stats = await fs.promises.stat(outputWordPath);
    console.log(`✓ 输出文件大小: ${Math.round(stats.size / 1024)}KB`);
  } catch (error) {
    console.error('❌ 转换失败:', error);
    throw error;
  }
}

async function testDirectConversion() {
  console.log('\n=== 测试直接转换功能 ===\n');

  // 简单的测试HTML
  const testHtml = `
    <html>
    <head>
        <meta charset="UTF-8">
    </head>
    <body>
        <div style="text-align: center;">
            <h1>测试文档</h1>
        </div>
        
        <div style="align-items: center; white-space: nowrap">
            <span style="visibility: visible">1. </span>
            <div style="display: inline-block">
                <p>这是第一道题目，请选择正确答案：</p>
            </div>
        </div>

        <div>
            <div style="align-items: center; white-space: nowrap; gap: 8px">
                <span><span style="padding-inline: 3px">A、</span></span>
                <div style="display: inline-block">
                    <p>选项A的内容</p>
                </div>
            </div>
            
            <div style="align-items: center; white-space: nowrap; gap: 8px">
                <span><span style="padding-inline: 3px">B、</span></span>
                <div style="display: inline-block">
                    <p>选项B的内容</p>
                </div>
            </div>
        </div>

        <table style="width: 100%;">
            <tr>
                <th>列1</th>
                <th>列2</th>
            </tr>
            <tr>
                <td>数据1</td>
                <td>数据2</td>
            </tr>
        </table>

        <p>这是一个包含<span data-emphasis-mark="dot">着重号</span>的句子。</p>
        <p>这是一个包含<span class="double-underline">下划线</span>的句子。</p>
    </body>
    </html>
  `;

  const outputPath = path.join(
    __dirname,
    '../../testFiles/direct-conversion-test.docx'
  );

  try {
    console.log('开始直接转换测试...');

    const startTime = Date.now();
    await generateWordFromHtml(testHtml, outputPath, {
      font: 'Microsoft YaHei',
      fontSize: 14,
      preprocessHtml: true,
    });
    const endTime = Date.now();

    console.log(`✓ 直接转换完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ 输出文件: ${outputPath}`);

    // 检查文件大小
    const stats = await fs.promises.stat(outputPath);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);
  } catch (error) {
    console.error('❌ 直接转换失败:', error);
    throw error;
  }
}

async function testBufferConversion() {
  console.log('\n=== 测试Buffer转换功能 ===\n');

  const simpleHtml = `
    <html>
    <body>
        <h1>Buffer转换测试</h1>
        <p>这是一个简单的HTML内容，用于测试Buffer转换功能。</p>
        <p><strong>粗体文本</strong> 和 <em>斜体文本</em></p>
    </body>
    </html>
  `;

  try {
    console.log('开始Buffer转换测试...');

    const startTime = Date.now();
    const wordBuffer = await convertHtmlToWord(simpleHtml, {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: true,
    });
    const endTime = Date.now();

    console.log(`✓ Buffer转换完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ Buffer大小: ${Math.round(wordBuffer.length / 1024)}KB`);

    // 保存Buffer到文件进行验证
    const outputPath = path.join(
      __dirname,
      '../../testFiles/buffer-conversion-test.docx'
    );
    await fs.promises.writeFile(outputPath, wordBuffer);
    console.log(`✓ Buffer已保存到: ${outputPath}`);
  } catch (error) {
    console.error('❌ Buffer转换失败:', error);
    throw error;
  }
}

// 运行所有测试
async function runAllTests() {
  try {
    await testHtmlToWordConversion();
    await testDirectConversion();
    await testBufferConversion();

    console.log('\n=== 所有测试完成 ===');
    console.log('生成的文件:');
    console.log('- testFiles/output-word-result-new.docx (主要测试文件)');
    console.log('- testFiles/direct-conversion-test.docx (直接转换测试)');
    console.log('- testFiles/buffer-conversion-test.docx (Buffer转换测试)');
  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  testHtmlToWordConversion,
  testDirectConversion,
  testBufferConversion,
  runAllTests,
};
