import * as fs from 'fs';
import * as path from 'path';

/**
 * 着重号后处理器
 * 在html-to-docx生成基础Word文档后，添加真正的着重号格式
 */
export class EmphasisPostProcessor {
  
  /**
   * 处理Word文档，添加着重号
   */
  async processWordDocument(
    inputPath: string, 
    outputPath: string,
    emphasisData: EmphasisInfo[]
  ): Promise<void> {
    console.log('开始着重号后处理...');
    console.log(`输入文件: ${inputPath}`);
    console.log(`输出文件: ${outputPath}`);
    console.log(`着重号数量: ${emphasisData.length}`);

    try {
      // 方案1: 使用docx库进行后处理
      await this.processWithDocxLibrary(inputPath, outputPath, emphasisData);
    } catch (error) {
      console.warn('docx库处理失败，尝试备用方案:', error.message);
      
      // 方案2: 直接复制文件（保持原有格式）
      await this.copyWithFallback(inputPath, outputPath);
    }
  }

  /**
   * 使用docx库进行后处理
   */
  private async processWithDocxLibrary(
    inputPath: string,
    outputPath: string,
    emphasisData: EmphasisInfo[]
  ): Promise<void> {
    try {
      // 尝试导入docx库
      const docx = await import('docx');
      
      // 读取原始Word文档
      const buffer = await fs.promises.readFile(inputPath);
      
      // 解析文档
      const doc = await docx.patchDocument(buffer, {
        patches: this.createEmphasisPatches(emphasisData)
      });

      // 保存处理后的文档
      await fs.promises.writeFile(outputPath, doc);
      console.log('✓ 使用docx库处理完成');
      
    } catch (error) {
      throw new Error(`docx库处理失败: ${error.message}`);
    }
  }

  /**
   * 创建着重号补丁
   */
  private createEmphasisPatches(emphasisData: EmphasisInfo[]): any[] {
    return emphasisData.map(info => ({
      type: 'text-emphasis',
      text: info.text,
      emphasis: {
        type: 'dot',
        position: 'below'
      }
    }));
  }

  /**
   * 备用方案：直接复制文件
   */
  private async copyWithFallback(inputPath: string, outputPath: string): Promise<void> {
    await fs.promises.copyFile(inputPath, outputPath);
    console.log('⚠️ 使用备用方案：直接复制文件（着重号显示为粗体下划线）');
  }

  /**
   * 从HTML中提取着重号信息
   */
  extractEmphasisInfo(htmlContent: string): EmphasisInfo[] {
    const emphasisList: EmphasisInfo[] = [];
    
    // 使用正则表达式查找着重号标记
    const emphasisRegex = /data-needs-emphasis="true"[^>]*data-emphasis-text="([^"]*)"[^>]*>([^<]*)</g;
    let match;
    
    while ((match = emphasisRegex.exec(htmlContent)) !== null) {
      emphasisList.push({
        text: match[1] || match[2],
        type: 'dot',
        position: 'below'
      });
    }

    console.log(`提取到 ${emphasisList.length} 个着重号标记`);
    return emphasisList;
  }

  /**
   * 生成着重号处理报告
   */
  generateReport(emphasisData: EmphasisInfo[], outputDir: string): void {
    const reportPath = path.join(outputDir, 'emphasis-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      totalEmphasis: emphasisData.length,
      emphasisList: emphasisData,
      processingMethod: 'two-stage-processing',
      stage1: 'html-to-docx (bold + underline)',
      stage2: 'post-processing (emphasis marks)'
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf-8');
    console.log(`✓ 着重号处理报告已保存: ${reportPath}`);
  }
}

/**
 * 着重号信息接口
 */
export interface EmphasisInfo {
  text: string;
  type: 'dot' | 'circle' | 'triangle';
  position: 'above' | 'below';
}

/**
 * 便捷函数：处理Word文档中的着重号
 */
export async function processEmphasisInWord(
  inputWordPath: string,
  outputWordPath: string,
  htmlContent: string
): Promise<void> {
  const processor = new EmphasisPostProcessor();
  
  // 提取着重号信息
  const emphasisData = processor.extractEmphasisInfo(htmlContent);
  
  // 处理Word文档
  await processor.processWordDocument(inputWordPath, outputWordPath, emphasisData);
  
  // 生成报告
  const outputDir = path.dirname(outputWordPath);
  processor.generateReport(emphasisData, outputDir);
}

// 导出单例实例
export const emphasisPostProcessor = new EmphasisPostProcessor();
