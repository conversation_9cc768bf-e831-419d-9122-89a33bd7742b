# HTML转Word转换效果调整指南

## 🎯 转换效果优化

如果转换效果不理想，可以通过以下方式进行微调：

### 📝 基本配置调整

在 `src/example/html-to-word-test.ts` 中修改配置：

```typescript
const options: HtmlToWordOptions = {
  // 字体设置
  font: 'Microsoft YaHei',           // 可选: 'SimSun', 'Times New Roman'
  fontSize: 12,                      // 可调: 10-16pt

  // 页面设置
  orientation: 'portrait',           // 可选: 'landscape'
  margins: {
    top: 1440,                       // 可调: 720-2160 (0.5-1.5英寸)
    right: 1440,
    bottom: 1440,
    left: 1440
  },

  // 表格设置
  table: {
    row: {
      cantSplit: true                // 防止表格跨页分割
    }
  },

  // 预处理设置
  preprocessHtml: true               // 建议保持启用
};
```

### 🔧 常见问题及解决方案

#### 1. 字体显示问题
**问题**: 中文字体显示不正确
**解决**: 
```typescript
font: 'SimSun',        // 宋体，兼容性更好
// 或
font: 'Microsoft YaHei', // 微软雅黑，现代感强
```

#### 2. 字体大小问题
**问题**: 文字太小或太大
**解决**:
```typescript
fontSize: 14,          // 增大字体
// 或
fontSize: 10,          // 减小字体
```

#### 3. 页边距问题
**问题**: 页面太挤或太空
**解决**:
```typescript
margins: {
  top: 720,            // 0.5英寸，更紧凑
  right: 720,
  bottom: 720,
  left: 720
},
// 或
margins: {
  top: 2160,           // 1.5英寸，更宽松
  right: 2160,
  bottom: 2160,
  left: 2160
},
```

#### 4. 试题格式问题
**问题**: 试题序号或选项格式不正确
**解决**: 在 `html-to-word-converter.ts` 中调整预处理逻辑

### 🎨 高级样式调整

#### 修改标题样式
在 `processHeaderAndStudentInfo` 方法中：
```typescript
titleP.style.cssText = 'text-align: center; font-size: 20pt; font-weight: bold; margin: 24pt 0; color: #000;';
```

#### 修改试题序号样式
在 `processQuestionLayout` 方法中：
```typescript
strongNumber.style.cssText = 'font-weight: bold; color: #000; font-size: 16pt;';
```

#### 修改选项样式
在 `processOptionsLayout` 方法中：
```typescript
strongLabel.style.cssText = 'font-weight: bold; color: #000; font-size: 14pt;';
```

### 📊 预设配置方案

#### 方案1: 紧凑型
```typescript
const compactOptions = {
  font: 'SimSun',
  fontSize: 11,
  margins: { top: 720, right: 720, bottom: 720, left: 720 },
  preprocessHtml: true
};
```

#### 方案2: 标准型
```typescript
const standardOptions = {
  font: 'Microsoft YaHei',
  fontSize: 12,
  margins: { top: 1440, right: 1440, bottom: 1440, left: 1440 },
  preprocessHtml: true
};
```

#### 方案3: 宽松型
```typescript
const spaciousOptions = {
  font: 'Microsoft YaHei',
  fontSize: 14,
  margins: { top: 2160, right: 1440, bottom: 2160, left: 1440 },
  preprocessHtml: true
};
```

### 🔍 调试技巧

#### 1. 查看预处理效果
在 `preprocessHtml` 方法末尾添加：
```typescript
console.log('预处理后的HTML:', document.body.innerHTML);
```

#### 2. 测试不同配置
创建多个配置进行对比：
```typescript
// 测试多种配置
const configs = [compactOptions, standardOptions, spaciousOptions];
for (let i = 0; i < configs.length; i++) {
  await converter.generateWordFile(
    htmlContent, 
    `output-word-result-${i}.docx`, 
    configs[i]
  );
}
```

#### 3. 禁用预处理对比
```typescript
// 对比预处理前后效果
await converter.generateWordFile(htmlContent, 'output-no-preprocess.docx', {
  ...options,
  preprocessHtml: false
});
```

### 📋 转换效果检查清单

转换完成后，检查以下项目：

- [ ] 标题居中且字体合适
- [ ] 学生信息行格式正确
- [ ] 试题序号加粗且间距合适
- [ ] 选项缩进且标签清晰
- [ ] 表格边框完整
- [ ] 着重号和下划线正确显示
- [ ] 整体排版美观

### 🚀 快速测试命令

```bash
# 运行转换测试
node --require ts-node/register src/example/html-to-word-test.ts

# 查看生成的文件
# 打开 testFiles/output-word-result.docx
```

### 💡 最佳实践

1. **先用标准配置测试**，再根据效果调整
2. **一次只调整一个参数**，便于定位问题
3. **保存多个配置方案**，适应不同需求
4. **定期备份工作配置**，避免意外丢失

### 📞 问题反馈

如果遇到无法解决的转换问题，请提供：
1. 输入HTML文件内容
2. 当前使用的配置
3. 期望的转换效果描述
4. 实际转换结果截图
