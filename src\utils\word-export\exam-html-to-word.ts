import * as htmlDocxJs from 'html-docx-js';
import { JSDOM } from 'jsdom';
import * as fs from 'fs';
import * as path from 'path';

export interface ExamHtmlToWordOptions {
  /**
   * 输出文件路径
   */
  outputPath?: string;
  
  /**
   * 字体设置
   */
  fontOptions?: {
    defaultFont?: string;
    fontSize?: number;
  };
  
  /**
   * 页面设置
   */
  pageOptions?: {
    margins?: {
      top?: string;
      right?: string;
      bottom?: string;
      left?: string;
    };
  };
  
  /**
   * 是否预处理HTML（清理和优化）
   */
  preprocessHtml?: boolean;
}

export class ExamHtmlToWordConverter {
  
  /**
   * 将试题HTML转换为Word文档
   */
  async convertExamHtmlToWord(
    htmlContent: string, 
    options: ExamHtmlToWordOptions = {}
  ): Promise<Buffer> {
    const {
      preprocessHtml = true,
      fontOptions = {},
      pageOptions = {}
    } = options;

    // 预处理HTML
    let processedHtml = htmlContent;
    if (preprocessHtml) {
      processedHtml = this.preprocessExamHtml(htmlContent);
    }

    // 包装成完整的HTML文档
    const fullHtml = this.wrapInExamHtmlDocument(processedHtml, options);

    try {
      // 使用html-docx-js进行转换
      const docxBlob = htmlDocxJs.asBlob(fullHtml);
      return Buffer.from(docxBlob);
    } catch (error) {
      console.error('试题HTML转Word失败:', error);
      throw new Error(`试题HTML转Word失败: ${error.message}`);
    }
  }

  /**
   * 预处理试题HTML内容
   */
  private preprocessExamHtml(htmlContent: string): string {
    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    // 1. 处理试题序号和内容的布局
    this.processQuestionLayout(document);

    // 2. 处理选项布局
    this.processOptionsLayout(document);

    // 3. 处理表格
    this.processTablesLayout(document);

    // 4. 处理下划线（填空题）
    this.processUnderlines(document);

    // 5. 处理着重号
    this.processEmphasisMarks(document);

    // 6. 清理不必要的样式
    this.cleanupStyles(document);

    return document.body.innerHTML;
  }

  /**
   * 处理试题序号和内容的布局
   */
  private processQuestionLayout(document: Document): void {
    // 查找所有试题容器
    const questionContainers = document.querySelectorAll('div[style*="align-items: center"][style*="white-space: nowrap"]');
    
    questionContainers.forEach(container => {
      const span = container.querySelector('span[style*="visibility: visible"]');
      const contentDiv = container.querySelector('div[style*="display: inline-block"]');
      
      if (span && contentDiv) {
        // 创建新的段落结构
        const newP = document.createElement('p');
        newP.style.margin = '12pt 0';
        newP.style.lineHeight = '1.5';
        
        // 添加序号
        const numberSpan = document.createElement('span');
        numberSpan.textContent = span.textContent?.trim() || '';
        numberSpan.style.fontWeight = 'bold';
        newP.appendChild(numberSpan);
        
        // 添加题目内容
        const contentSpan = document.createElement('span');
        contentSpan.innerHTML = contentDiv.innerHTML;
        newP.appendChild(contentSpan);
        
        // 替换原容器
        container.parentNode?.replaceChild(newP, container);
      }
    });
  }

  /**
   * 处理选项布局
   */
  private processOptionsLayout(document: Document): void {
    // 查找选项容器
    const optionContainers = document.querySelectorAll('div[style*="align-items: center"][style*="gap: 8px"]');
    
    optionContainers.forEach(container => {
      const optionLabel = container.querySelector('span span');
      const optionContent = container.querySelector('div[style*="display: inline-block"]');
      
      if (optionLabel && optionContent) {
        // 创建新的段落
        const newP = document.createElement('p');
        newP.style.margin = '6pt 0 6pt 20pt';
        newP.style.lineHeight = '1.5';
        
        // 添加选项标签
        const labelSpan = document.createElement('span');
        labelSpan.textContent = optionLabel.textContent?.trim() || '';
        labelSpan.style.fontWeight = 'bold';
        newP.appendChild(labelSpan);
        
        // 添加选项内容
        const contentSpan = document.createElement('span');
        contentSpan.innerHTML = optionContent.innerHTML;
        newP.appendChild(contentSpan);
        
        // 替换原容器
        container.parentNode?.replaceChild(newP, container);
      }
    });
  }

  /**
   * 处理表格布局
   */
  private processTablesLayout(document: Document): void {
    const tables = document.querySelectorAll('table');
    
    tables.forEach(table => {
      // 确保表格有边框
      table.style.borderCollapse = 'collapse';
      table.style.width = '100%';
      table.style.margin = '12pt 0';
      
      // 处理所有单元格
      const cells = table.querySelectorAll('td, th');
      cells.forEach(cell => {
        cell.style.border = '1pt solid black';
        cell.style.padding = '6pt';
        cell.style.verticalAlign = 'middle';
      });
    });
  }

  /**
   * 处理下划线（填空题）
   */
  private processUnderlines(document: Document): void {
    // 处理class="double-underline"的元素
    const doubleUnderlines = document.querySelectorAll('.double-underline');
    doubleUnderlines.forEach(el => {
      el.style.textDecoration = 'underline';
      el.style.fontWeight = 'bold';
    });

    // 处理其他下划线元素
    const underlines = document.querySelectorAll('u');
    underlines.forEach(el => {
      el.style.textDecoration = 'underline';
    });
  }

  /**
   * 处理着重号
   */
  private processEmphasisMarks(document: Document): void {
    const emphasisElements = document.querySelectorAll('[data-emphasis-mark="dot"]');
    
    emphasisElements.forEach(el => {
      // 在Word中，我们用粗体和下划线来模拟着重号
      el.style.fontWeight = 'bold';
      el.style.textDecoration = 'underline';
      
      // 添加一个视觉提示
      const textContent = el.textContent || '';
      el.textContent = `【${textContent}】`;
    });
  }

  /**
   * 清理不必要的样式
   */
  private cleanupStyles(document: Document): void {
    const allElements = document.querySelectorAll('*');
    
    allElements.forEach(el => {
      // 移除绝对定位
      if (el.getAttribute('style')?.includes('position: absolute')) {
        el.remove();
        return;
      }
      
      // 简化复杂的样式
      const style = el.getAttribute('style');
      if (style) {
        const cleanedStyle = this.cleanStyle(style);
        if (cleanedStyle) {
          el.setAttribute('style', cleanedStyle);
        } else {
          el.removeAttribute('style');
        }
      }
    });
  }

  /**
   * 清理样式字符串
   */
  private cleanStyle(styleString: string): string {
    const keepStyles = [
      'color', 'background-color', 'font-size', 'font-weight', 'font-style', 'font-family',
      'text-align', 'text-decoration', 'margin', 'padding', 'border',
      'width', 'height', 'line-height'
    ];

    const styles = styleString.split(';').filter(s => s.trim());
    const cleanedStyles = styles.filter(style => {
      const property = style.split(':')[0]?.trim().toLowerCase();
      return keepStyles.some(keep => property?.includes(keep));
    });

    return cleanedStyles.join('; ');
  }

  /**
   * 将HTML内容包装成完整的HTML文档
   */
  private wrapInExamHtmlDocument(htmlContent: string, options: ExamHtmlToWordOptions): string {
    const { fontOptions = {}, pageOptions = {} } = options;
    
    const margins = pageOptions.margins || {};
    
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: ${fontOptions.defaultFont || 'Microsoft YaHei, SimSun, serif'};
            font-size: ${fontOptions.fontSize || 14}pt;
            line-height: 1.5;
            margin: ${margins.top || '2.5cm'} ${margins.right || '2cm'} ${margins.bottom || '2.5cm'} ${margins.left || '2cm'};
            color: black;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin: 18pt 0 12pt 0;
            line-height: 1.3;
        }
        
        p {
            margin: 6pt 0;
            line-height: 1.5;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 12pt 0;
        }
        
        table, th, td {
            border: 1pt solid black;
        }
        
        th, td {
            padding: 6pt;
            text-align: left;
            vertical-align: middle;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .question-number {
            font-weight: bold;
            margin-right: 8pt;
        }
        
        .option {
            margin: 6pt 0 6pt 20pt;
        }
        
        .option-label {
            font-weight: bold;
            margin-right: 8pt;
        }
        
        u, .underline {
            text-decoration: underline;
        }
        
        .emphasis {
            font-weight: bold;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
  }

  /**
   * 便捷方法：直接从HTML文件生成Word文件
   */
  async convertExamHtmlFileToWord(
    htmlFilePath: string, 
    outputPath: string, 
    options: ExamHtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');
      const wordBuffer = await this.convertExamHtmlToWord(htmlContent, options);
      await fs.promises.writeFile(outputPath, wordBuffer);
      console.log(`试题Word文档已生成: ${outputPath}`);
    } catch (error) {
      console.error('从HTML文件转换失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const examHtmlToWordConverter = new ExamHtmlToWordConverter();

// 导出便捷函数
export async function convertExamHtmlToWord(
  htmlContent: string, 
  options: ExamHtmlToWordOptions = {}
): Promise<Buffer> {
  return examHtmlToWordConverter.convertExamHtmlToWord(htmlContent, options);
}

export async function generateExamWordFromHtml(
  htmlContent: string, 
  outputPath: string, 
  options: ExamHtmlToWordOptions = {}
): Promise<void> {
  const wordBuffer = await examHtmlToWordConverter.convertExamHtmlToWord(htmlContent, options);
  await fs.promises.writeFile(outputPath, wordBuffer);
  console.log(`试题Word文档已生成: ${outputPath}`);
}
