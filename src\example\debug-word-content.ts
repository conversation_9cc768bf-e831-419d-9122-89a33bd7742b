import {
  HtmlToWordConverter,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');

/**
 * 调试Word内容显示问题
 */
async function debugWordContent() {
  console.log('=== 调试Word内容显示问题 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 测试1: 最基础的HTML
    console.log('🧪 测试1: 最基础HTML');
    const basicHtml = `
      <h1>标题测试</h1>
      <p>这是第一段文字。</p>
      <p>这是第二段文字，包含<strong>粗体</strong>和<em>斜体</em>。</p>
    `;

    await converter.generateWordFile(basicHtml, path.join(testFilesDir, 'debug-basic.docx'), {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: false
    });
    console.log('✓ 基础测试完成: debug-basic.docx');

    // 测试2: 带序号的内容
    console.log('\n🧪 测试2: 带序号内容');
    const numberedHtml = `
      <h1>试题测试</h1>
      <p><strong>1.</strong> 这是第一题的内容。</p>
      <p style="margin-left: 20px;"><strong>A.</strong> 选项A的内容</p>
      <p style="margin-left: 20px;"><strong>B.</strong> 选项B的内容</p>
      <p><strong>2.</strong> 这是第二题的内容。</p>
      <p style="margin-left: 20px;"><strong>A.</strong> 选项A的内容</p>
      <p style="margin-left: 20px;"><strong>B.</strong> 选项B的内容</p>
    `;

    await converter.generateWordFile(numberedHtml, path.join(testFilesDir, 'debug-numbered.docx'), {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: false
    });
    console.log('✓ 序号测试完成: debug-numbered.docx');

    // 测试3: 复杂嵌套结构
    console.log('\n🧪 测试3: 复杂嵌套结构');
    const nestedHtml = `
      <div>
        <span>1.</span>
        <span>
          <p>这是嵌套在span中的p标签内容。</p>
        </span>
      </div>
      <div>
        <span>2.</span>
        <span>
          <div>
            <p>这是更深层嵌套的内容。</p>
          </div>
        </span>
      </div>
    `;

    await converter.generateWordFile(nestedHtml, path.join(testFilesDir, 'debug-nested.docx'), {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: false
    });
    console.log('✓ 嵌套测试完成: debug-nested.docx');

    // 测试4: 从预处理HTML中提取一小段
    console.log('\n🧪 测试4: 预处理HTML片段');
    const preprocessedPath = path.join(testFilesDir, 'processed-html-preview.html');
    if (fs.existsSync(preprocessedPath)) {
      const fullHtml = await fs.promises.readFile(preprocessedPath, 'utf-8');
      
      // 提取第一题的内容
      const firstQuestionMatch = fullHtml.match(/<div>\s*<span>\s*1\.\s*<\/span>[\s\S]*?<\/div>/);
      if (firstQuestionMatch) {
        const firstQuestionHtml = firstQuestionMatch[0];
        console.log('提取的第一题HTML长度:', firstQuestionHtml.length);
        console.log('第一题HTML内容预览:', firstQuestionHtml.substring(0, 200) + '...');

        await converter.generateWordFile(firstQuestionHtml, path.join(testFilesDir, 'debug-first-question.docx'), {
          font: 'Microsoft YaHei',
          fontSize: 12,
          preprocessHtml: false
        });
        console.log('✓ 第一题测试完成: debug-first-question.docx');
      } else {
        console.log('❌ 无法提取第一题内容');
      }
    } else {
      console.log('❌ 预处理HTML文件不存在');
    }

    // 测试5: 字体问题测试
    console.log('\n🧪 测试5: 字体问题测试');
    const fontTestHtml = `
      <p style="font-family: 'Times New Roman'; font-size: 11pt;">Times New Roman字体测试</p>
      <p style="font-family: 'Microsoft YaHei'; font-size: 12pt;">微软雅黑字体测试</p>
      <p style="font-family: '宋体'; font-size: 14pt;">宋体字体测试</p>
    `;

    await converter.generateWordFile(fontTestHtml, path.join(testFilesDir, 'debug-fonts.docx'), {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: false
    });
    console.log('✓ 字体测试完成: debug-fonts.docx');

    console.log('\n=== 调试测试完成 ===');
    console.log('📋 检查步骤:');
    console.log('1. 打开 debug-basic.docx - 检查基础内容是否显示');
    console.log('2. 打开 debug-numbered.docx - 检查序号内容是否显示');
    console.log('3. 打开 debug-nested.docx - 检查嵌套结构是否导致问题');
    console.log('4. 打开 debug-first-question.docx - 检查预处理内容片段');
    console.log('5. 打开 debug-fonts.docx - 检查字体是否影响显示');
    console.log('\n通过对比这些文件，可以确定问题出现在哪个环节。');

  } catch (error) {
    console.error('❌ 调试测试失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await debugWordContent();
  } catch (error) {
    console.error('\n❌ 调试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { debugWordContent, main };
