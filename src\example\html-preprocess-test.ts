import {
  HtmlToWordConverter,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');
const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');

/**
 * HTML预处理测试 - 专注于样式处理
 * 不进行Word转换，只测试预处理效果
 */
async function testHtmlPreprocessing() {
  console.log('=== HTML预处理测试 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 检查输入文件
    if (!fs.existsSync(inputHtmlPath)) {
      throw new Error(`输入HTML文件不存在: ${inputHtmlPath}`);
    }

    console.log(`输入文件: ${inputHtmlPath}`);

    // 读取HTML内容
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`原始HTML文件大小: ${htmlContent.length} 字符\n`);

    console.log('开始预处理...');
    const startTime = Date.now();

    // 调用预处理方法（这是私有方法，我们需要通过反射访问）
    const preprocessedHtml = (converter as any).preprocessHtml(htmlContent);

    const endTime = Date.now();
    console.log(`✓ 预处理完成！耗时: ${endTime - startTime}ms`);
    console.log(`✓ 预处理后HTML长度: ${preprocessedHtml.length} 字符`);
    console.log(`✓ 内容保留比例: ${Math.round((preprocessedHtml.length / htmlContent.length) * 100)}%\n`);

    // 保存预处理结果到文件
    const outputPath = path.join(testFilesDir, 'preprocess-test-result.html');
    const fullHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>预处理测试结果</title>
  <style>
    body { 
      font-family: 'Microsoft YaHei', sans-serif; 
      margin: 20px; 
      line-height: 1.6;
    }
    .info { 
      background: #f0f8ff; 
      padding: 15px; 
      margin-bottom: 20px; 
      border-left: 4px solid #007acc;
      border-radius: 4px;
    }
    .comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin: 20px 0;
    }
    .original, .processed {
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 4px;
    }
    .original { background: #fff8f0; }
    .processed { background: #f0fff0; }
    h3 { margin-top: 0; color: #333; }
    .stats {
      background: #f9f9f9;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="info">
    <h2>📊 预处理统计信息</h2>
    <div class="stats">
      <p><strong>原始HTML长度:</strong> ${htmlContent.length.toLocaleString()} 字符</p>
      <p><strong>预处理后长度:</strong> ${preprocessedHtml.length.toLocaleString()} 字符</p>
      <p><strong>保留比例:</strong> ${Math.round((preprocessedHtml.length / htmlContent.length) * 100)}%</p>
      <p><strong>处理时间:</strong> ${endTime - startTime}ms</p>
    </div>
  </div>

  <h2>🔍 预处理结果</h2>
  <div class="processed-content">
    ${preprocessedHtml}
  </div>

  <div class="info" style="margin-top: 30px;">
    <h3>🎯 检查要点</h3>
    <ul>
      <li><strong>着重号:</strong> "新鲜感"应该显示为粗体下划线（只有3个有着重号）</li>
      <li><strong>内联布局:</strong> 试题序号和内容应该在同一行</li>
      <li><strong>选项格式:</strong> A、B、C、D选项应该有适当的缩进</li>
      <li><strong>表格:</strong> 表格应该有边框</li>
      <li><strong>下划线:</strong> 填空题的下划线应该保持</li>
    </ul>
  </div>
</body>
</html>`;

    fs.writeFileSync(outputPath, fullHtml, 'utf-8');
    console.log(`✓ 预处理结果已保存到: ${outputPath}`);

    console.log('\n=== 预处理测试完成 ===');
    console.log('📋 下一步:');
    console.log('1. 打开预处理结果文件查看效果');
    console.log('2. 对比原始HTML文件，检查样式是否一致');
    console.log('3. 如有问题，调整预处理逻辑');

  } catch (error) {
    console.error('❌ 预处理测试失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await testHtmlPreprocessing();
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { testHtmlPreprocessing, main };
