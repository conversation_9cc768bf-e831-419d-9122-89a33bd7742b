import {
  HtmlToWordConverter,
  HtmlToWordOptions,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');

/**
 * 简单Word转换测试
 * 使用最基本的HTML结构测试Word转换
 */
async function testSimpleWordConversion() {
  console.log('=== 简单Word转换测试 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 创建简单的测试HTML
    const simpleHtml = `
      <h1>2025年06月10日作业</h1>
      <p>姓名: _______ 班级: _______ 学号: _______</p>
      
      <p><strong>1.</strong> 下面对有<span style="text-emphasis: filled dot; text-emphasis-position: under;">新鲜感</span>的词语的理解，错误的一项是（　　）</p>
      
      <p style="margin-left: 20px;"><strong>A.</strong> 边疆（靠近国界的领土）</p>
      <p style="margin-left: 20px;"><strong>B.</strong> 绚丽多彩（各种各样的色彩灿烂美丽）</p>
      <p style="margin-left: 20px;"><strong>C.</strong> 坪坝（高高低低的小路）</p>
      <p style="margin-left: 20px;"><strong>D.</strong> 高高低低</p>
      
      <p><strong>2.</strong> 下列对有新鲜感的句子的理解，错误的一项是（　　）</p>
      
      <p style="margin-left: 20px;"><strong>A.</strong> 这时候，窗外十分安静，树枝不摇了，鸟儿不叫了，蝴蝶停在花朵上，好像都在听同学们读课文。（把植物比作人）</p>
      <p style="margin-left: 20px;"><strong>B.</strong> 同学们向在校园里欢唱的小鸟打招呼，向敬爱的老师问好，向高高飘扬的国旗敬礼。（连用三个"向……"，是排比的句式）</p>
      <p style="margin-left: 20px;"><strong>C.</strong> 大家在大青树下做游戏，连松鼠也赶来看热闹。（把动物当作人来写）</p>
      
      <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
          <th>商品</th>
          <th>成本价</th>
          <th>销售价</th>
        </tr>
        <tr>
          <td>A</td>
          <td>25</td>
          <td>35</td>
        </tr>
        <tr>
          <td>B</td>
          <td>30</td>
          <td>45</td>
        </tr>
      </table>
    `;

    console.log('简单HTML内容长度:', simpleHtml.length);

    // 转换配置
    const options: HtmlToWordOptions = {
      font: 'Microsoft YaHei',
      fontSize: 12,
      orientation: 'portrait',
      margins: {
        top: 1440,
        right: 1440,
        bottom: 1440,
        left: 1440
      },
      preprocessHtml: false  // 禁用预处理，直接转换
    };

    console.log('开始简单转换（无预处理）...');
    const startTime = Date.now();

    const outputPath = path.join(testFilesDir, 'simple-word-test.docx');
    await converter.generateWordFile(simpleHtml, outputPath, options);

    const endTime = Date.now();
    console.log(`✓ 简单转换完成！耗时: ${endTime - startTime}ms`);

    // 检查输出文件
    const stats = await fs.promises.stat(outputPath);
    console.log(`✓ 输出文件: ${outputPath}`);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);

    console.log('\n=== 简单转换测试完成 ===');
    console.log('📋 对比测试:');
    console.log('1. 打开 simple-word-test.docx 查看基础转换效果');
    console.log('2. 对比 output-word-result-new.docx 查看复杂转换效果');
    console.log('3. 确定是预处理问题还是html-to-docx库问题');

  } catch (error) {
    console.error('❌ 简单转换测试失败:', error);
    throw error;
  }
}

/**
 * 测试预处理后的HTML转换
 */
async function testPreprocessedWordConversion() {
  console.log('\n=== 预处理HTML转换测试 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 读取预处理后的HTML
    const preprocessedHtmlPath = path.join(testFilesDir, 'processed-html-preview.html');
    if (!fs.existsSync(preprocessedHtmlPath)) {
      throw new Error('预处理HTML文件不存在，请先运行预处理测试');
    }

    const fullHtml = await fs.promises.readFile(preprocessedHtmlPath, 'utf-8');
    
    // 提取body内容
    const bodyMatch = fullHtml.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (!bodyMatch) {
      throw new Error('无法提取body内容');
    }

    const bodyContent = bodyMatch[1];
    console.log('预处理HTML body内容长度:', bodyContent.length);

    // 转换配置
    const options: HtmlToWordOptions = {
      font: 'Microsoft YaHei',
      fontSize: 12,
      orientation: 'portrait',
      margins: {
        top: 1440,
        right: 1440,
        bottom: 1440,
        left: 1440
      },
      preprocessHtml: false  // 已经预处理过了
    };

    console.log('开始预处理HTML转换...');
    const startTime = Date.now();

    const outputPath = path.join(testFilesDir, 'preprocessed-word-test.docx');
    await converter.generateWordFile(bodyContent, outputPath, options);

    const endTime = Date.now();
    console.log(`✓ 预处理HTML转换完成！耗时: ${endTime - startTime}ms`);

    // 检查输出文件
    const stats = await fs.promises.stat(outputPath);
    console.log(`✓ 输出文件: ${outputPath}`);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);

  } catch (error) {
    console.error('❌ 预处理HTML转换失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 测试1: 简单HTML转换
    await testSimpleWordConversion();
    
    // 测试2: 预处理HTML转换
    await testPreprocessedWordConversion();
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { testSimpleWordConversion, testPreprocessedWordConversion, main };
