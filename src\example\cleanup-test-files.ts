import * as fs from 'fs';
import * as path from 'path';

// 测试文件目录
const testFilesDir = path.join(__dirname, '../../testFiles');

/**
 * 清理测试文件
 */
async function cleanupTestFiles() {
  console.log('=== 清理测试文件 ===\n');

  // 需要保留的重要文件
  const keepFiles = [
    'input-html-sample.html',           // 原始输入文件
    'two-stage-result.docx',            // 最终两阶段结果
    'output-word-result-new.docx',      // 传统方法结果
    'processed-html-preview.html',      // 预处理预览
    'preprocess-test-result.html',      // 预处理测试报告
    'emphasis-processing-report.json'   // 着重号处理报告
  ];

  // 可以删除的测试文件
  const testFiles = [
    'debug-basic.docx',
    'debug-numbered.docx',
    'debug-nested.docx',
    'debug-first-question.docx',
    'debug-fonts.docx',
    'traditional-method.docx',
    'two-stage-method.docx',
    'minimal-test.docx',
    'simple-word-test.docx',
    'preprocessed-word-test.docx',
    'simple-docx-test.docx',
    'diagnosis-output.html'
  ];

  try {
    console.log('📁 扫描测试文件目录:', testFilesDir);
    
    if (!fs.existsSync(testFilesDir)) {
      console.log('❌ 测试文件目录不存在');
      return;
    }

    const allFiles = fs.readdirSync(testFilesDir);
    console.log(`📄 目录中共有 ${allFiles.length} 个文件\n`);

    // 显示保留的文件
    console.log('✅ 保留的重要文件:');
    keepFiles.forEach(file => {
      if (allFiles.includes(file)) {
        const filePath = path.join(testFilesDir, file);
        const stats = fs.statSync(filePath);
        const size = Math.round(stats.size / 1024);
        console.log(`   📄 ${file} (${size}KB)`);
      } else {
        console.log(`   ❓ ${file} (不存在)`);
      }
    });

    // 删除测试文件
    console.log('\n🗑️ 删除的测试文件:');
    let deletedCount = 0;
    
    testFiles.forEach(file => {
      const filePath = path.join(testFilesDir, file);
      if (fs.existsSync(filePath)) {
        try {
          const stats = fs.statSync(filePath);
          const size = Math.round(stats.size / 1024);
          fs.unlinkSync(filePath);
          console.log(`   🗑️ ${file} (${size}KB) - 已删除`);
          deletedCount++;
        } catch (error) {
          console.log(`   ❌ ${file} - 删除失败: ${error.message}`);
        }
      }
    });

    // 删除其他可能的临时文件
    console.log('\n🔍 检查其他临时文件:');
    const remainingFiles = fs.readdirSync(testFilesDir);
    const tempPatterns = [
      /-temp\.docx$/,
      /-test\.docx$/,
      /^temp-/,
      /^test-/
    ];

    remainingFiles.forEach(file => {
      if (!keepFiles.includes(file)) {
        const isTemp = tempPatterns.some(pattern => pattern.test(file));
        if (isTemp) {
          try {
            const filePath = path.join(testFilesDir, file);
            const stats = fs.statSync(filePath);
            const size = Math.round(stats.size / 1024);
            fs.unlinkSync(filePath);
            console.log(`   🗑️ ${file} (${size}KB) - 临时文件已删除`);
            deletedCount++;
          } catch (error) {
            console.log(`   ❌ ${file} - 删除失败: ${error.message}`);
          }
        }
      }
    });

    console.log(`\n✅ 清理完成！删除了 ${deletedCount} 个文件`);
    
    // 显示最终保留的文件
    const finalFiles = fs.readdirSync(testFilesDir);
    console.log(`\n📋 最终保留 ${finalFiles.length} 个文件:`);
    finalFiles.forEach(file => {
      const filePath = path.join(testFilesDir, file);
      const stats = fs.statSync(filePath);
      const size = Math.round(stats.size / 1024);
      const isImportant = keepFiles.includes(file) ? '⭐' : '📄';
      console.log(`   ${isImportant} ${file} (${size}KB)`);
    });

    console.log('\n🎯 重点查看文件:');
    console.log('   1. two-stage-result.docx - 最新的两阶段处理结果');
    console.log('   2. output-word-result-new.docx - 传统方法结果');
    console.log('   3. processed-html-preview.html - 预处理效果预览');
    console.log('   4. emphasis-processing-report.json - 着重号处理报告');

  } catch (error) {
    console.error('❌ 清理过程中出错:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await cleanupTestFiles();
  } catch (error) {
    console.error('\n❌ 清理失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { cleanupTestFiles, main };
