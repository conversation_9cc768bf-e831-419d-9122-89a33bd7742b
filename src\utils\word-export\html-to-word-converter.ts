import { JSD<PERSON> } from 'jsdom';
import * as fs from 'fs';

// html-to-docx的正确导入方式
const HTMLtoDOCX = require('html-to-docx');

export interface HtmlToWordOptions {
  /**
   * 字体设置
   */
  font?: string;
  fontSize?: number;

  /**
   * 页面设置
   */
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  /**
   * 表格设置
   */
  table?: {
    row?: {
      cantSplit?: boolean;
    };
  };

  /**
   * 是否预处理HTML
   */
  preprocessHtml?: boolean;
}

export class HtmlToWordConverter {
  /**
   * 将HTML转换为Word文档Buffer
   */
  async convertHtmlToWord(
    htmlContent: string,
    options: HtmlToWordOptions = {}
  ): Promise<Buffer> {
    const {
      font = 'Microsoft YaHei',
      fontSize = 12,
      orientation = 'portrait',
      margins = { top: 1440, right: 1440, bottom: 1440, left: 1440 },
      table = { row: { cantSplit: true } },
      preprocessHtml = true,
    } = options;

    // 预处理HTML
    let processedHtml = htmlContent;
    if (preprocessHtml) {
      processedHtml = this.preprocessHtml(htmlContent);
    }

    // 配置html-to-docx选项
    const docxOptions = {
      orientation,
      margins,
      font,
      fontSize,
      table,
      footer: false,
      pageNumber: false,
    };

    try {
      console.log('开始HTML转Word转换...');
      const docxBuffer = await HTMLtoDOCX(processedHtml, null, docxOptions);
      console.log('HTML转Word转换完成');
      return Buffer.from(docxBuffer);
    } catch (error) {
      console.error('HTML转Word转换失败:', error);
      throw new Error(`HTML转Word失败: ${error.message}`);
    }
  }

  /**
   * 预处理HTML内容，优化Word转换效果
   */
  private preprocessHtml(htmlContent: string): string {
    console.log('开始预处理HTML...');
    console.log('原始HTML长度:', htmlContent.length);

    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    console.log('预处理前body内容长度:', document.body.innerHTML.length);

    // 优化预处理流程，解决display: inline-block和着重号问题
    // 1. 首先处理着重号（在清理属性之前）
    this.processEmphasisMarks(document);

    // 2. 处理display: inline-block布局
    this.processInlineBlockLayout(document);

    // 3. 清理有问题的属性（防止XML错误）
    this.cleanupStyles(document);

    // 4. 移除不必要的元素
    this.removeUnnecessaryElements(document);

    // 5. 处理表格（确保有边框）
    this.processTablesLayout(document);

    const processedHtml = document.body.innerHTML;
    console.log('预处理后HTML长度:', processedHtml.length);

    // 保存预处理后的HTML到文件，方便查看
    try {
      const fs = require('fs');
      const path = require('path');
      const outputPath = path.join(
        __dirname,
        '../../../testFiles/processed-html-preview.html'
      );
      const fullHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>预处理后的HTML预览</title>
  <style>
    body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
    .info { background: #f0f0f0; padding: 10px; margin-bottom: 20px; }
  </style>
</head>
<body>
  <div class="info">
    <h3>预处理信息</h3>
    <p>原始HTML长度: ${htmlContent.length} 字符</p>
    <p>预处理后长度: ${processedHtml.length} 字符</p>
    <p>保留比例: ${Math.round(
      (processedHtml.length / htmlContent.length) * 100
    )}%</p>
  </div>
  ${processedHtml}
</body>
</html>`;
      fs.writeFileSync(outputPath, fullHtml, 'utf-8');
      console.log('预处理后的HTML已保存到:', outputPath);
    } catch (error) {
      console.warn('保存预处理HTML失败:', error.message);
    }

    // 如果预处理后内容为空，返回原始内容
    if (!processedHtml.trim()) {
      console.warn('预处理后内容为空，返回原始HTML');
      return htmlContent;
    }

    return processedHtml;
  }

  /**
   * 处理着重号标记
   */
  private processEmphasisMarks(document: Document): void {
    console.log('处理着重号标记...');

    // 方法1: 查找所有带有 data-emphasis-mark="dot" 的元素
    const emphasisElements = document.querySelectorAll('[data-emphasis-mark="dot"]');
    console.log('找到data-emphasis-mark元素数量:', emphasisElements.length);

    emphasisElements.forEach(el => {
      const text = el.textContent || '';
      console.log('处理着重号文本(data-emphasis-mark):', text);

      // 方案1: 尝试使用span + 特殊样式来模拟着重号
      const emphasisSpan = document.createElement('span');
      emphasisSpan.textContent = text;

      // 使用CSS样式模拟着重号效果，包含多种备选方案
      emphasisSpan.style.cssText = `
        font-weight: bold;
        text-decoration: underline;
        text-emphasis: filled dot;
        text-emphasis-position: under;
        border-bottom: 2px dotted currentColor;
        position: relative;
      `;

      // 方案2: 如果上面不行，添加一个包含着重号字符的方案
      // 创建一个包装div，在文字下方添加着重号
      const wrapperSpan = document.createElement('span');
      wrapperSpan.style.cssText = 'position: relative; display: inline-block;';

      const textSpan = document.createElement('span');
      textSpan.textContent = text;
      textSpan.style.cssText = 'font-weight: bold;';

      const dotsSpan = document.createElement('span');
      dotsSpan.textContent = '•'.repeat(text.length); // 使用圆点字符
      dotsSpan.style.cssText = `
        position: absolute;
        bottom: -8px;
        left: 0;
        font-size: 8px;
        letter-spacing: 0.1em;
        color: currentColor;
      `;

      wrapperSpan.appendChild(textSpan);
      wrapperSpan.appendChild(dotsSpan);

      // 替换原元素 - 先尝试简单的方案
      el.parentNode?.replaceChild(emphasisSpan, el);
      console.log('着重号处理完成(data-emphasis-mark):', text);
    });

    // 方法2: 查找所有带有 text-emphasis CSS样式的元素
    const allElements = document.querySelectorAll('*');
    let emphasisByStyleCount = 0;

    allElements.forEach(el => {
      const style = el.getAttribute('style') || '';
      if (style.includes('text-emphasis:') || style.includes('text-emphasis-position:')) {
        const text = el.textContent || '';
        console.log('处理着重号文本(CSS样式):', text);

        // 创建新的强调元素，使用粗体下划线表示着重号
        const strongEl = document.createElement('strong');
        const underlineEl = document.createElement('u');
        underlineEl.textContent = text;
        strongEl.appendChild(underlineEl);

        // 添加样式确保在Word中显示正确
        strongEl.style.cssText = 'font-weight: bold; text-decoration: underline;';

        // 替换原元素
        el.parentNode?.replaceChild(strongEl, el);
        console.log('着重号处理完成(CSS样式):', text);
        emphasisByStyleCount++;
      }
    });

    console.log('找到CSS样式着重号元素数量:', emphasisByStyleCount);
  }

  /**
   * 处理display: inline-block布局
   */
  private processInlineBlockLayout(document: Document): void {
    console.log('处理inline-block布局...');

    // 查找所有 display: inline-block 的元素
    const inlineBlockElements = document.querySelectorAll('[style*="display: inline-block"]');
    console.log('找到inline-block元素数量:', inlineBlockElements.length);

    inlineBlockElements.forEach(el => {
      const htmlEl = el as HTMLElement;

      // 检查父元素是否有 white-space: nowrap，这通常表示是水平布局
      const parent = el.parentElement;
      const parentStyle = parent?.getAttribute('style') || '';

      if (parentStyle.includes('white-space: nowrap') || parentStyle.includes('gap:')) {
        // 这是水平布局，应该保持内联
        // 将div转换为span以确保内联显示
        if (el.tagName.toLowerCase() === 'div') {
          const span = document.createElement('span');
          span.innerHTML = htmlEl.innerHTML;

          // 保留重要的样式，但确保是内联的
          const style = htmlEl.getAttribute('style') || '';
          const cleanedStyle = style.replace('display: inline-block', 'display: inline');
          span.setAttribute('style', cleanedStyle);

          el.parentNode?.replaceChild(span, el);
        }
      }
    });
  }

  /**
   * 处理表格布局
   */
  private processTablesLayout(document: Document): void {
    const tables = document.querySelectorAll('table');

    tables.forEach(table => {
      // 设置表格样式
      const htmlTable = table as HTMLElement;
      htmlTable.style.cssText =
        'border-collapse: collapse; width: 100%; margin: 12pt 0;';

      // 处理所有单元格
      const cells = table.querySelectorAll('td, th');
      cells.forEach(cell => {
        const htmlCell = cell as HTMLElement;
        htmlCell.style.cssText =
          'border: 1pt solid black; padding: 6pt; vertical-align: middle;';

        // 表头加粗
        if (cell.tagName === 'TH') {
          htmlCell.style.fontWeight = 'bold';
          htmlCell.style.backgroundColor = '#f0f0f0';
        }
      });
    });
  }

  /**
   * 清理样式
   */
  private cleanupStyles(document: Document): void {
    const allElements = document.querySelectorAll('*');

    allElements.forEach(el => {
      // 清理所有属性，移除可能导致XML错误的属性
      const attributesToRemove = [];
      for (let i = 0; i < el.attributes.length; i++) {
        const attr = el.attributes[i];
        // 移除包含@符号或其他特殊字符的属性，但不移除data-emphasis-mark（已处理）
        if (attr.name.includes('@') || attr.name.includes(':')) {
          attributesToRemove.push(attr.name);
        }
        // 只移除未处理的data-属性
        if (attr.name.startsWith('data-') && attr.name !== 'data-emphasis-mark') {
          attributesToRemove.push(attr.name);
        }
      }

      attributesToRemove.forEach(attrName => {
        el.removeAttribute(attrName);
      });

      const style = el.getAttribute('style');
      if (style) {
        // 保留基本样式
        const cleanedStyle = this.extractBasicStyles(style);
        if (cleanedStyle) {
          el.setAttribute('style', cleanedStyle);
        } else {
          el.removeAttribute('style');
        }
      }
    });
  }

  /**
   * 提取基本样式
   */
  private extractBasicStyles(styleString: string): string {
    const supportedStyles = [
      'color',
      'background-color',
      'font-size',
      'font-weight',
      'font-style',
      'font-family',
      'text-align',
      'text-decoration',
      'margin',
      'padding',
      'border',
      'width',
      'height',
      'line-height',
    ];

    const styles = styleString.split(';').filter(s => s.trim());
    const basicStyles = styles.filter(style => {
      const property = style.split(':')[0]?.trim().toLowerCase();
      return supportedStyles.some(supported => property?.includes(supported));
    });

    return basicStyles.join('; ');
  }

  /**
   * 移除不必要的元素
   */
  private removeUnnecessaryElements(document: Document): void {
    // 只移除明确的绝对定位元素
    const absoluteElements = document.querySelectorAll(
      '[style*="position: absolute"]'
    );
    console.log('移除绝对定位元素数量:', absoluteElements.length);
    absoluteElements.forEach(el => el.remove());

    // 移除contenteditable属性
    const editableElements = document.querySelectorAll('[contenteditable]');
    editableElements.forEach(el => el.removeAttribute('contenteditable'));

    // 移除spellcheck属性
    const spellcheckElements = document.querySelectorAll('[spellcheck]');
    spellcheckElements.forEach(el => el.removeAttribute('spellcheck'));

    // 不要移除空的div，因为它们可能有重要的布局作用
  }

  /**
   * 便捷方法：直接生成Word文件
   */
  async generateWordFile(
    htmlContent: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const wordBuffer = await this.convertHtmlToWord(htmlContent, options);
      await fs.promises.writeFile(outputPath, wordBuffer);
      console.log(`Word文档已生成: ${outputPath}`);
    } catch (error) {
      console.error('生成Word文件失败:', error);
      throw error;
    }
  }

  /**
   * 便捷方法：从HTML文件生成Word文件
   */
  async convertHtmlFileToWord(
    htmlFilePath: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');
      await this.generateWordFile(htmlContent, outputPath, options);
    } catch (error) {
      console.error('从HTML文件转换失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const htmlToWordConverter = new HtmlToWordConverter();

// 导出便捷函数
export async function convertHtmlToWord(
  htmlContent: string,
  options: HtmlToWordOptions = {}
): Promise<Buffer> {
  return htmlToWordConverter.convertHtmlToWord(htmlContent, options);
}

export async function generateWordFromHtml(
  htmlContent: string,
  outputPath: string,
  options: HtmlToWordOptions = {}
): Promise<void> {
  return htmlToWordConverter.generateWordFile(htmlContent, outputPath, options);
}
