import { JSD<PERSON> } from 'jsdom';
import * as fs from 'fs';

// html-to-docx的正确导入方式
const HTMLtoDOCX = require('html-to-docx');

export interface HtmlToWordOptions {
  /**
   * 字体设置
   */
  font?: string;
  fontSize?: number;

  /**
   * 页面设置
   */
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  /**
   * 表格设置
   */
  table?: {
    row?: {
      cantSplit?: boolean;
    };
  };

  /**
   * 是否预处理HTML
   */
  preprocessHtml?: boolean;
}

export class HtmlToWordConverter {
  /**
   * 将HTML转换为Word文档Buffer
   */
  async convertHtmlToWord(
    htmlContent: string,
    options: HtmlToWordOptions = {}
  ): Promise<Buffer> {
    const {
      font = 'Microsoft YaHei',
      fontSize = 12,
      orientation = 'portrait',
      margins = { top: 1440, right: 1440, bottom: 1440, left: 1440 },
      table = { row: { cantSplit: true } },
      preprocessHtml = true,
    } = options;

    // 预处理HTML
    let processedHtml = htmlContent;
    if (preprocessHtml) {
      processedHtml = this.preprocessHtml(htmlContent);
    }

    // 配置html-to-docx选项
    const docxOptions = {
      orientation,
      margins,
      font,
      fontSize,
      table,
      footer: false,
      pageNumber: false,
    };

    try {
      console.log('开始HTML转Word转换...');
      const docxBuffer = await HTMLtoDOCX(processedHtml, null, docxOptions);
      console.log('HTML转Word转换完成');
      return Buffer.from(docxBuffer);
    } catch (error) {
      console.error('HTML转Word转换失败:', error);
      throw new Error(`HTML转Word失败: ${error.message}`);
    }
  }

  /**
   * 预处理HTML内容，优化Word转换效果
   */
  private preprocessHtml(htmlContent: string): string {
    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    // 1. 处理标题和学生信息
    this.processHeaderAndStudentInfo(document);

    // 2. 处理试题序号和内容布局
    this.processQuestionLayout(document);

    // 3. 处理选项布局
    this.processOptionsLayout(document);

    // 4. 处理表格
    this.processTablesLayout(document);

    // 5. 处理着重号和特殊标记
    this.processSpecialMarks(document);

    // 6. 处理下划线填空
    this.processUnderlines(document);

    // 7. 清理样式
    this.cleanupStyles(document);

    // 8. 移除不必要的元素
    this.removeUnnecessaryElements(document);

    return document.body.innerHTML;
  }

  /**
   * 处理标题和学生信息
   */
  private processHeaderAndStudentInfo(document: Document): void {
    // 处理标题 - 查找包含日期的标题
    const titleElements = document.querySelectorAll(
      'h1, h2, h3, h4, h5, h6, div'
    );
    titleElements.forEach(el => {
      const text = el.textContent?.trim() || '';
      // 检查是否是标题（包含年月日或"作业"）
      if (
        text.match(/\d{4}年\d{1,2}月\d{1,2}日.*作业/) ||
        text.includes('作业')
      ) {
        const titleP = document.createElement('h1');
        titleP.textContent = text;
        titleP.style.cssText =
          'text-align: center; font-size: 18pt; font-weight: bold; margin: 20pt 0; color: #000;';
        el.parentNode?.replaceChild(titleP, el);
      }
    });

    // 处理学生信息行（姓名、班级、学号）
    const infoElements = document.querySelectorAll('div, p');
    infoElements.forEach(el => {
      const text = el.textContent?.trim() || '';
      // 检查是否是学生信息行
      if (
        text.includes('姓名:') ||
        text.includes('班级:') ||
        text.includes('学号:')
      ) {
        const infoP = document.createElement('p');
        infoP.innerHTML = el.innerHTML;
        infoP.style.cssText =
          'text-align: center; font-size: 12pt; margin: 10pt 0; color: #000;';
        el.parentNode?.replaceChild(infoP, el);
      }
    });
  }

  /**
   * 处理试题序号和内容布局
   */
  private processQuestionLayout(document: Document): void {
    // 查找试题容器：div[style*="align-items: center"][style*="white-space: nowrap"]
    const questionContainers = document.querySelectorAll(
      'div[style*="align-items: center"][style*="white-space: nowrap"]'
    );

    questionContainers.forEach(container => {
      const numberSpan = container.querySelector(
        'span[style*="visibility: visible"]'
      );
      const contentDiv = container.querySelector(
        'div[style*="display: inline-block"]'
      );

      if (numberSpan && contentDiv) {
        // 创建新的段落
        const newP = document.createElement('p');
        newP.style.cssText =
          'margin: 12pt 0; line-height: 1.5; text-align: left;';

        // 添加序号（加粗）
        const numberText = numberSpan.textContent?.trim() || '';
        const strongNumber = document.createElement('strong');
        strongNumber.textContent = numberText + ' ';
        strongNumber.style.cssText =
          'font-weight: bold; color: #000; font-size: 14pt;';
        newP.appendChild(strongNumber);

        // 添加内容，保持原有格式
        const contentSpan = document.createElement('span');
        contentSpan.innerHTML = contentDiv.innerHTML;
        contentSpan.style.cssText = 'color: #000; font-size: 14pt;';
        newP.appendChild(contentSpan);

        // 替换原容器
        container.parentNode?.replaceChild(newP, container);
      }
    });
  }

  /**
   * 处理选项布局
   */
  private processOptionsLayout(document: Document): void {
    // 查找选项容器：div[style*="gap: 8px"]
    const optionContainers = document.querySelectorAll(
      'div[style*="gap: 8px"]'
    );

    optionContainers.forEach(container => {
      const optionLabel = container.querySelector('span span');
      const optionContent = container.querySelector(
        'div[style*="display: inline-block"]'
      );

      if (optionLabel && optionContent) {
        // 创建新的段落
        const newP = document.createElement('p');
        newP.style.cssText =
          'margin: 6pt 0 6pt 24pt; line-height: 1.5; text-align: left;';

        // 添加选项标签（加粗）
        const labelText = optionLabel.textContent?.trim() || '';
        const strongLabel = document.createElement('strong');
        strongLabel.textContent = labelText + ' ';
        strongLabel.style.cssText =
          'font-weight: bold; color: #000; font-size: 13pt;';
        newP.appendChild(strongLabel);

        // 添加选项内容
        const contentSpan = document.createElement('span');
        contentSpan.innerHTML = optionContent.innerHTML;
        contentSpan.style.cssText = 'color: #000; font-size: 13pt;';
        newP.appendChild(contentSpan);

        // 替换原容器
        container.parentNode?.replaceChild(newP, container);
      }
    });
  }

  /**
   * 处理表格布局
   */
  private processTablesLayout(document: Document): void {
    const tables = document.querySelectorAll('table');

    tables.forEach(table => {
      // 设置表格样式
      const htmlTable = table as HTMLElement;
      htmlTable.style.cssText =
        'border-collapse: collapse; width: 100%; margin: 12pt 0;';

      // 处理所有单元格
      const cells = table.querySelectorAll('td, th');
      cells.forEach(cell => {
        const htmlCell = cell as HTMLElement;
        htmlCell.style.cssText =
          'border: 1pt solid black; padding: 6pt; vertical-align: middle;';

        // 表头加粗
        if (cell.tagName === 'TH') {
          htmlCell.style.fontWeight = 'bold';
          htmlCell.style.backgroundColor = '#f0f0f0';
        }
      });
    });
  }

  /**
   * 处理着重号和特殊标记
   */
  private processSpecialMarks(document: Document): void {
    // 处理着重号
    const emphasisElements = document.querySelectorAll(
      '[data-emphasis-mark="dot"]'
    );
    emphasisElements.forEach(el => {
      const text = el.textContent || '';
      el.innerHTML = `<strong><u>${text}</u></strong>`;
      el.removeAttribute('data-emphasis-mark');
    });

    // 处理双下划线
    const doubleUnderlines = document.querySelectorAll('.double-underline');
    doubleUnderlines.forEach(el => {
      el.innerHTML = `<strong><u>${el.textContent}</u></strong>`;
      el.removeAttribute('class');
    });
  }

  /**
   * 处理下划线填空
   */
  private processUnderlines(document: Document): void {
    const underlines = document.querySelectorAll('u');
    underlines.forEach(el => {
      // 确保下划线样式
      el.style.textDecoration = 'underline';
    });
  }

  /**
   * 清理样式
   */
  private cleanupStyles(document: Document): void {
    const allElements = document.querySelectorAll('*');

    allElements.forEach(el => {
      const style = el.getAttribute('style');
      if (style) {
        // 保留基本样式
        const cleanedStyle = this.extractBasicStyles(style);
        if (cleanedStyle) {
          el.setAttribute('style', cleanedStyle);
        } else {
          el.removeAttribute('style');
        }
      }
    });
  }

  /**
   * 提取基本样式
   */
  private extractBasicStyles(styleString: string): string {
    const supportedStyles = [
      'color',
      'background-color',
      'font-size',
      'font-weight',
      'font-style',
      'font-family',
      'text-align',
      'text-decoration',
      'margin',
      'padding',
      'border',
      'width',
      'height',
      'line-height',
    ];

    const styles = styleString.split(';').filter(s => s.trim());
    const basicStyles = styles.filter(style => {
      const property = style.split(':')[0]?.trim().toLowerCase();
      return supportedStyles.some(supported => property?.includes(supported));
    });

    return basicStyles.join('; ');
  }

  /**
   * 移除不必要的元素
   */
  private removeUnnecessaryElements(document: Document): void {
    // 移除绝对定位元素
    const absoluteElements = document.querySelectorAll(
      '[style*="position: absolute"]'
    );
    absoluteElements.forEach(el => el.remove());

    // 移除空的div
    const emptyDivs = document.querySelectorAll('div:empty');
    emptyDivs.forEach(el => el.remove());

    // 移除contenteditable属性
    const editableElements = document.querySelectorAll('[contenteditable]');
    editableElements.forEach(el => el.removeAttribute('contenteditable'));

    // 移除spellcheck属性
    const spellcheckElements = document.querySelectorAll('[spellcheck]');
    spellcheckElements.forEach(el => el.removeAttribute('spellcheck'));
  }

  /**
   * 便捷方法：直接生成Word文件
   */
  async generateWordFile(
    htmlContent: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const wordBuffer = await this.convertHtmlToWord(htmlContent, options);
      await fs.promises.writeFile(outputPath, wordBuffer);
      console.log(`Word文档已生成: ${outputPath}`);
    } catch (error) {
      console.error('生成Word文件失败:', error);
      throw error;
    }
  }

  /**
   * 便捷方法：从HTML文件生成Word文件
   */
  async convertHtmlFileToWord(
    htmlFilePath: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');
      await this.generateWordFile(htmlContent, outputPath, options);
    } catch (error) {
      console.error('从HTML文件转换失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const htmlToWordConverter = new HtmlToWordConverter();

// 导出便捷函数
export async function convertHtmlToWord(
  htmlContent: string,
  options: HtmlToWordOptions = {}
): Promise<Buffer> {
  return htmlToWordConverter.convertHtmlToWord(htmlContent, options);
}

export async function generateWordFromHtml(
  htmlContent: string,
  outputPath: string,
  options: HtmlToWordOptions = {}
): Promise<void> {
  return htmlToWordConverter.generateWordFile(htmlContent, outputPath, options);
}
