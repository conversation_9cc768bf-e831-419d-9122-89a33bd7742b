import { JSDOM } from 'jsdom';
import * as fs from 'fs';
import * as path from 'path';

// html-to-docx的正确导入方式
const HTMLtoDOCX = require('html-to-docx');

export interface HtmlToWordOptions {
  /**
   * 字体设置
   */
  font?: string;
  fontSize?: number;

  /**
   * 页面设置
   */
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  /**
   * 表格设置
   */
  table?: {
    row?: {
      cantSplit?: boolean;
    };
  };

  /**
   * 是否预处理HTML
   */
  preprocessHtml?: boolean;
}

/**
 * 着重号信息接口
 */
export interface EmphasisInfo {
  text: string;
  type: 'dot' | 'circle' | 'triangle';
  position: 'above' | 'below';
}

export class HtmlToWordConverter {
  /**
   * 将HTML转换为Word文档Buffer
   */
  async convertHtmlToWord(
    htmlContent: string,
    options: HtmlToWordOptions = {}
  ): Promise<Buffer> {
    const {
      font = 'Microsoft YaHei',
      fontSize = 12,
      orientation = 'portrait',
      margins = { top: 1440, right: 1440, bottom: 1440, left: 1440 },
      table = { row: { cantSplit: true } },
      preprocessHtml = true,
    } = options;

    // 预处理HTML
    let processedHtml = htmlContent;
    if (preprocessHtml) {
      processedHtml = this.preprocessHtml(htmlContent);
    }

    // 配置html-to-docx选项
    const docxOptions = {
      orientation,
      margins,
      font,
      fontSize,
      table,
      footer: false,
      pageNumber: false,
    };

    try {
      console.log('开始HTML转Word转换...');
      const docxBuffer = await HTMLtoDOCX(processedHtml, null, docxOptions);
      console.log('HTML转Word转换完成');
      return Buffer.from(docxBuffer);
    } catch (error) {
      console.error('HTML转Word转换失败:', error);
      throw new Error(`HTML转Word失败: ${error.message}`);
    }
  }

  /**
   * 预处理HTML内容，优化Word转换效果
   */
  private preprocessHtml(htmlContent: string): string {
    console.log('开始预处理HTML...');
    console.log('原始HTML长度:', htmlContent.length);

    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    console.log('预处理前body内容长度:', document.body.innerHTML.length);

    // 优化预处理流程，解决display: inline-block和着重号问题
    // 1. 首先处理着重号（在清理属性之前）
    this.processEmphasisMarks(document);

    // 2. 处理display: inline-block布局
    this.processInlineBlockLayout(document);

    // 3. 清理有问题的属性（防止XML错误）
    this.cleanupStyles(document);

    // 4. 移除不必要的元素
    this.removeUnnecessaryElements(document);

    // 5. 处理表格（确保有边框）
    this.processTablesLayout(document);

    const processedHtml = document.body.innerHTML;
    console.log('预处理后HTML长度:', processedHtml.length);

    // 保存预处理后的HTML到文件，方便查看
    try {
      const fs = require('fs');
      const path = require('path');
      const outputPath = path.join(
        __dirname,
        '../../../testFiles/processed-html-preview.html'
      );
      const fullHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>预处理后的HTML预览</title>
  <style>
    body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
    .info { background: #f0f0f0; padding: 10px; margin-bottom: 20px; }
  </style>
</head>
<body>
  <div class="info">
    <h3>预处理信息</h3>
    <p>原始HTML长度: ${htmlContent.length} 字符</p>
    <p>预处理后长度: ${processedHtml.length} 字符</p>
    <p>保留比例: ${Math.round(
      (processedHtml.length / htmlContent.length) * 100
    )}%</p>
  </div>
  ${processedHtml}
</body>
</html>`;
      fs.writeFileSync(outputPath, fullHtml, 'utf-8');
      console.log('预处理后的HTML已保存到:', outputPath);
    } catch (error) {
      console.warn('保存预处理HTML失败:', error.message);
    }

    // 如果预处理后内容为空，返回原始内容
    if (!processedHtml.trim()) {
      console.warn('预处理后内容为空，返回原始HTML');
      return htmlContent;
    }

    return processedHtml;
  }

  /**
   * 处理着重号标记
   */
  private processEmphasisMarks(document: Document): void {
    console.log('处理着重号标记...');

    // 统一处理：查找所有带有着重号标记的元素（data-emphasis-mark 或 CSS样式）
    const allElements = document.querySelectorAll('*');
    let processedCount = 0;

    allElements.forEach(el => {
      const style = el.getAttribute('style') || '';
      const hasDataMark = el.hasAttribute('data-emphasis-mark') && el.getAttribute('data-emphasis-mark') === 'dot';
      const hasCssEmphasis = style.includes('text-emphasis:') || style.includes('text-emphasis-position:');

      // 只要有任一种着重号标记就处理
      if (hasDataMark || hasCssEmphasis) {
        const text = el.textContent || '';
        console.log('处理着重号文本:', text);

        // 方案1: 保留原始CSS样式，让html-to-docx尝试处理
        const emphasisSpan = document.createElement('span');
        emphasisSpan.textContent = text;

        // 两阶段处理方案：
        // 阶段1: 为html-to-docx提供可识别的格式（粗体下划线）
        emphasisSpan.style.cssText = 'font-weight: bold; text-decoration: underline;';

        // 阶段2: 保留着重号标记供后处理使用
        emphasisSpan.setAttribute('data-emphasis-type', 'dot');
        emphasisSpan.setAttribute('data-emphasis-text', text);
        emphasisSpan.setAttribute('data-needs-emphasis', 'true');

        // 替换原元素
        el.parentNode?.replaceChild(emphasisSpan, el);
        console.log('着重号处理完成:', text);
        processedCount++;
      }
    });

    console.log('总共处理着重号元素数量:', processedCount);
  }



  /**
   * 处理display: inline-block布局
   */
  private processInlineBlockLayout(document: Document): void {
    console.log('处理inline-block布局...');

    // 查找所有 display: inline-block 的元素
    const inlineBlockElements = document.querySelectorAll('[style*="display: inline-block"]');
    console.log('找到inline-block元素数量:', inlineBlockElements.length);

    inlineBlockElements.forEach(el => {
      const htmlEl = el as HTMLElement;

      // 检查父元素是否有 white-space: nowrap，这通常表示是水平布局
      const parent = el.parentElement;
      const parentStyle = parent?.getAttribute('style') || '';

      if (parentStyle.includes('white-space: nowrap') || parentStyle.includes('gap:')) {
        // 这是水平布局，应该保持内联
        // 将div转换为span以确保内联显示
        if (el.tagName.toLowerCase() === 'div') {
          const span = document.createElement('span');
          span.innerHTML = htmlEl.innerHTML;

          // 保留重要的样式，但确保是内联的
          const style = htmlEl.getAttribute('style') || '';
          const cleanedStyle = style.replace('display: inline-block', 'display: inline');
          span.setAttribute('style', cleanedStyle);

          el.parentNode?.replaceChild(span, el);
        }
      }
    });
  }

  /**
   * 处理表格布局
   */
  private processTablesLayout(document: Document): void {
    const tables = document.querySelectorAll('table');

    tables.forEach(table => {
      // 设置表格样式
      const htmlTable = table as HTMLElement;
      htmlTable.style.cssText =
        'border-collapse: collapse; width: 100%; margin: 12pt 0;';

      // 处理所有单元格
      const cells = table.querySelectorAll('td, th');
      cells.forEach(cell => {
        const htmlCell = cell as HTMLElement;
        htmlCell.style.cssText =
          'border: 1pt solid black; padding: 6pt; vertical-align: middle;';

        // 表头加粗
        if (cell.tagName === 'TH') {
          htmlCell.style.fontWeight = 'bold';
          htmlCell.style.backgroundColor = '#f0f0f0';
        }
      });
    });
  }

  /**
   * 清理样式
   */
  private cleanupStyles(document: Document): void {
    const allElements = document.querySelectorAll('*');

    allElements.forEach(el => {
      // 清理所有属性，移除可能导致XML错误的属性
      const attributesToRemove = [];
      for (let i = 0; i < el.attributes.length; i++) {
        const attr = el.attributes[i];
        // 移除包含@符号或其他特殊字符的属性，但不移除data-emphasis-mark（已处理）
        if (attr.name.includes('@') || attr.name.includes(':')) {
          attributesToRemove.push(attr.name);
        }
        // 只移除未处理的data-属性，保留着重号相关属性
        if (attr.name.startsWith('data-') &&
            attr.name !== 'data-emphasis-mark' &&
            attr.name !== 'data-emphasis-type' &&
            attr.name !== 'data-emphasis-text' &&
            attr.name !== 'data-needs-emphasis') {
          attributesToRemove.push(attr.name);
        }
      }

      attributesToRemove.forEach(attrName => {
        el.removeAttribute(attrName);
      });

      const style = el.getAttribute('style');
      if (style) {
        // 保留基本样式
        const cleanedStyle = this.extractBasicStyles(style);
        if (cleanedStyle) {
          el.setAttribute('style', cleanedStyle);
        } else {
          el.removeAttribute('style');
        }
      }
    });
  }

  /**
   * 提取基本样式
   */
  private extractBasicStyles(styleString: string): string {
    const supportedStyles = [
      'color',
      'background-color',
      'font-size',
      'font-weight',
      'font-style',
      'font-family',
      'text-align',
      'text-decoration',
      'margin',
      'padding',
      'border',
      'width',
      'height',
      'line-height',
      // 添加着重号相关样式
      'text-emphasis',
      'text-emphasis-position',
      '-webkit-text-emphasis',
      '-webkit-text-emphasis-position',
    ];

    const styles = styleString.split(';').filter(s => s.trim());
    const basicStyles = styles.filter(style => {
      const property = style.split(':')[0]?.trim().toLowerCase();
      // 精确匹配或前缀匹配支持的样式
      return supportedStyles.some(supported =>
        property === supported || property?.startsWith(supported)
      );
    });

    return basicStyles.join('; ');
  }

  /**
   * 移除不必要的元素
   */
  private removeUnnecessaryElements(document: Document): void {
    // 只移除明确的绝对定位元素
    const absoluteElements = document.querySelectorAll(
      '[style*="position: absolute"]'
    );
    console.log('移除绝对定位元素数量:', absoluteElements.length);
    absoluteElements.forEach(el => el.remove());

    // 移除contenteditable属性
    const editableElements = document.querySelectorAll('[contenteditable]');
    editableElements.forEach(el => el.removeAttribute('contenteditable'));

    // 移除spellcheck属性
    const spellcheckElements = document.querySelectorAll('[spellcheck]');
    spellcheckElements.forEach(el => el.removeAttribute('spellcheck'));

    // 不要移除空的div，因为它们可能有重要的布局作用
  }

  /**
   * 便捷方法：直接生成Word文件
   */
  async generateWordFile(
    htmlContent: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const wordBuffer = await this.convertHtmlToWord(htmlContent, options);
      await fs.promises.writeFile(outputPath, wordBuffer);
      console.log(`Word文档已生成: ${outputPath}`);
    } catch (error) {
      console.error('生成Word文件失败:', error);
      throw error;
    }
  }

  /**
   * 两阶段处理：生成Word文件并后处理着重号
   */
  async generateWordFileWithEmphasis(
    htmlContent: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      console.log('开始两阶段Word生成...');

      // 预处理HTML以获取着重号标记
      let processedHtml = htmlContent;
      if (options.preprocessHtml !== false) {
        processedHtml = this.preprocessHtml(htmlContent);
      }

      // 阶段1: 使用html-to-docx生成基础Word文档
      const tempPath = outputPath.replace('.docx', '-temp.docx');
      const tempOptions = { ...options, preprocessHtml: false }; // 已经预处理过了
      const wordBuffer = await this.convertHtmlToWord(processedHtml, tempOptions);
      await fs.promises.writeFile(tempPath, wordBuffer);
      console.log('✓ 阶段1完成：基础Word文档生成');

      // 阶段2: 后处理着重号（使用预处理后的HTML）
      await this.postProcessEmphasis(tempPath, outputPath, processedHtml);
      console.log('✓ 阶段2完成：着重号后处理');

      // 清理临时文件
      try {
        await fs.promises.unlink(tempPath);
        console.log('✓ 临时文件已清理');
      } catch (error) {
        console.warn('清理临时文件失败:', error.message);
      }

    } catch (error) {
      console.error('两阶段Word生成失败:', error);
      throw error;
    }
  }

  /**
   * 后处理着重号
   */
  private async postProcessEmphasis(
    inputPath: string,
    outputPath: string,
    htmlContent: string
  ): Promise<void> {
    try {
      // 提取着重号信息
      const emphasisInfo = this.extractEmphasisInfo(htmlContent);
      console.log(`提取到 ${emphasisInfo.length} 个着重号标记`);

      if (emphasisInfo.length === 0) {
        // 没有着重号，直接复制文件
        await fs.promises.copyFile(inputPath, outputPath);
        console.log('无着重号需要处理，直接复制文件');
        return;
      }

      // 目前先复制文件，保持粗体下划线格式
      // 后续可以集成docx库进行真正的着重号处理
      await fs.promises.copyFile(inputPath, outputPath);

      // 生成着重号处理报告
      this.generateEmphasisReport(emphasisInfo, path.dirname(outputPath));

      console.log('着重号后处理完成（当前显示为粗体下划线）');

    } catch (error) {
      console.error('着重号后处理失败:', error);
      // 失败时至少复制基础文件
      await fs.promises.copyFile(inputPath, outputPath);
    }
  }

  /**
   * 从HTML中提取着重号信息
   */
  private extractEmphasisInfo(htmlContent: string): EmphasisInfo[] {
    const emphasisList: EmphasisInfo[] = [];

    console.log('开始提取着重号信息...');
    console.log('HTML内容长度:', htmlContent.length);

    // 先检查是否包含着重号标记
    const hasEmphasisMarks = htmlContent.includes('data-needs-emphasis="true"');
    console.log('是否包含着重号标记:', hasEmphasisMarks);

    if (hasEmphasisMarks) {
      // 查找所有着重号标记的位置
      const markPositions = [];
      let index = 0;
      while ((index = htmlContent.indexOf('data-needs-emphasis="true"', index)) !== -1) {
        markPositions.push(index);
        index += 1;
      }
      console.log('找到着重号标记位置:', markPositions.length, '个');

      // 显示每个标记周围的内容
      markPositions.forEach((pos, i) => {
        const start = Math.max(0, pos - 50);
        const end = Math.min(htmlContent.length, pos + 200);
        const context = htmlContent.substring(start, end);
        console.log(`标记${i + 1}周围内容:`, context);
      });
    }

    // 查找所有着重号标记 - 更灵活的正则表达式
    const emphasisRegex = /<span[^>]*data-needs-emphasis="true"[^>]*data-emphasis-text="([^"]*)"[^>]*>([^<]*)<\/span>/g;
    let match: RegExpExecArray | null;

    while ((match = emphasisRegex.exec(htmlContent)) !== null) {
      const text = match[1] || match[2];
      console.log('正则匹配到:', match[0]);
      console.log('提取文本:', text);
      if (text && text.trim()) {
        emphasisList.push({
          text: text.trim(),
          type: 'dot',
          position: 'below'
        });
      }
    }

    // 备用方案：如果上面的正则没找到，尝试更宽松的匹配
    if (emphasisList.length === 0) {
      console.log('主正则未匹配，尝试备用方案...');
      const backupRegex = /data-needs-emphasis="true"[^>]*>([^<]+)</g;
      let backupMatch: RegExpExecArray | null;

      while ((backupMatch = backupRegex.exec(htmlContent)) !== null) {
        const text = backupMatch[1];
        console.log('备用正则匹配到:', backupMatch[0]);
        console.log('备用提取文本:', text);
        if (text && text.trim()) {
          emphasisList.push({
            text: text.trim(),
            type: 'dot',
            position: 'below'
          });
        }
      }
    }

    console.log('最终提取到着重号数量:', emphasisList.length);
    return emphasisList;
  }

  /**
   * 生成着重号处理报告
   */
  private generateEmphasisReport(emphasisInfo: EmphasisInfo[], outputDir: string): void {
    const reportPath = path.join(outputDir, 'emphasis-processing-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      totalEmphasis: emphasisInfo.length,
      emphasisList: emphasisInfo,
      processingMethod: 'two-stage-processing',
      stage1: 'html-to-docx (bold + underline as placeholder)',
      stage2: 'post-processing (ready for emphasis marks)',
      note: '当前着重号显示为粗体下划线，可通过docx库进一步处理为真正的着重号'
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf-8');
    console.log(`✓ 着重号处理报告已保存: ${reportPath}`);
  }

  /**
   * 便捷方法：从HTML文件生成Word文件
   */
  async convertHtmlFileToWord(
    htmlFilePath: string,
    outputPath: string,
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    try {
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');
      await this.generateWordFile(htmlContent, outputPath, options);
    } catch (error) {
      console.error('从HTML文件转换失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const htmlToWordConverter = new HtmlToWordConverter();

// 导出便捷函数
export async function convertHtmlToWord(
  htmlContent: string,
  options: HtmlToWordOptions = {}
): Promise<Buffer> {
  return htmlToWordConverter.convertHtmlToWord(htmlContent, options);
}

export async function generateWordFromHtml(
  htmlContent: string,
  outputPath: string,
  options: HtmlToWordOptions = {}
): Promise<void> {
  return htmlToWordConverter.generateWordFile(htmlContent, outputPath, options);
}
