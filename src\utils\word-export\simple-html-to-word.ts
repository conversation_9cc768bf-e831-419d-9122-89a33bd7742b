import * as htmlDocxJs from 'html-docx-js';
import { JSDOM } from 'jsdom';

// 尝试多种导入方式来兼容不同的模块系统
let HTMLtoDOCX: any;
try {
  HTMLtoDOCX = require('html-to-docx');
} catch (e) {
  try {
    HTMLtoDOCX = require('html-to-docx').default;
  } catch (e2) {
    console.warn('html-to-docx 导入失败，将只使用 html-docx-js');
  }
}

export interface HtmlToWordOptions {
  /**
   * 转换方式：
   * - 'html-to-docx': 使用html-to-docx库（推荐，功能更强大）
   * - 'html-docx-js': 使用html-docx-js库（更轻量）
   */
  method?: 'html-to-docx' | 'html-docx-js';
  
  /**
   * 页面设置
   */
  pageOptions?: {
    width?: number;
    height?: number;
    margins?: {
      top?: number;
      right?: number;
      bottom?: number;
      left?: number;
    };
  };
  
  /**
   * 字体设置
   */
  fontOptions?: {
    defaultFont?: string;
    fontSize?: number;
  };
  
  /**
   * 是否预处理HTML（清理和优化）
   */
  preprocessHtml?: boolean;
}

export class SimpleHtmlToWordConverter {
  
  /**
   * 将HTML转换为Word文档Buffer
   */
  async convertHtmlToWord(
    htmlContent: string,
    options: HtmlToWordOptions = {}
  ): Promise<Buffer> {
    const {
      method = 'html-to-docx',
      preprocessHtml = true
    } = options;

    // 预处理HTML
    let processedHtml = htmlContent;
    if (preprocessHtml) {
      processedHtml = this.preprocessHtml(htmlContent);
    }

    // 根据选择的方法进行转换
    if (method === 'html-to-docx') {
      return await this.convertWithHtmlToDocx(processedHtml, options);
    } else {
      return this.convertWithHtmlDocxJs(processedHtml, options);
    }
  }

  /**
   * 使用html-to-docx进行转换
   */
  private async convertWithHtmlToDocx(
    htmlContent: string,
    options: HtmlToWordOptions
  ): Promise<Buffer> {
    if (!HTMLtoDOCX) {
      throw new Error('html-to-docx 库未正确加载，请使用 html-docx-js 方法');
    }

    const { pageOptions = {}, fontOptions = {} } = options;

    const docxOptions = {
      table: { row: { cantSplit: true } },
      footer: true,
      pageNumber: true,
      font: fontOptions.defaultFont || 'Times New Roman',
      fontSize: fontOptions.fontSize || 12,
      ...pageOptions
    };

    try {
      const docxBuffer = await HTMLtoDOCX(htmlContent, null, docxOptions);
      return Buffer.from(docxBuffer);
    } catch (error) {
      console.error('html-to-docx转换失败:', error);
      throw new Error(`HTML转Word失败: ${error.message}`);
    }
  }

  /**
   * 使用html-docx-js进行转换
   */
  private convertWithHtmlDocxJs(
    htmlContent: string, 
    options: HtmlToWordOptions
  ): Buffer {
    try {
      // html-docx-js需要完整的HTML文档结构
      const fullHtml = this.wrapInHtmlDocument(htmlContent, options);
      const docxBlob = htmlDocxJs.asBlob(fullHtml);
      return Buffer.from(docxBlob);
    } catch (error) {
      console.error('html-docx-js转换失败:', error);
      throw new Error(`HTML转Word失败: ${error.message}`);
    }
  }

  /**
   * 预处理HTML内容
   */
  private preprocessHtml(htmlContent: string): string {
    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    // 1. 移除绝对定位元素（这些在Word中可能造成布局问题）
    const absoluteElements = document.querySelectorAll('[style*="position: absolute"], [style*="position:absolute"]');
    absoluteElements.forEach(el => el.remove());

    // 2. 处理图片：确保有合适的尺寸
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (!img.style.width && !img.style.height) {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
      }
    });

    // 3. 简化复杂的CSS样式，保留基本格式
    const allElements = document.querySelectorAll('*');
    allElements.forEach(el => {
      if (el.getAttribute('style')) {
        const style = el.getAttribute('style');
        // 保留基本样式，移除复杂的布局样式
        const basicStyles = this.extractBasicStyles(style);
        if (basicStyles) {
          el.setAttribute('style', basicStyles);
        } else {
          el.removeAttribute('style');
        }
      }
    });

    // 4. 处理表格：确保有边框
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
      if (!table.getAttribute('border')) {
        table.setAttribute('border', '1');
      }
    });

    return document.body.innerHTML;
  }

  /**
   * 提取基本样式（保留Word支持的样式）
   */
  private extractBasicStyles(styleString: string): string {
    const supportedStyles = [
      'color', 'background-color', 'font-size', 'font-weight', 'font-style',
      'text-align', 'text-decoration', 'margin', 'padding', 'border',
      'width', 'height'
    ];

    const styles = styleString.split(';').filter(s => s.trim());
    const basicStyles = styles.filter(style => {
      const property = style.split(':')[0]?.trim().toLowerCase();
      return supportedStyles.some(supported => property?.includes(supported));
    });

    return basicStyles.join('; ');
  }

  /**
   * 将HTML内容包装成完整的HTML文档
   */
  private wrapInHtmlDocument(htmlContent: string, options: HtmlToWordOptions): string {
    const { fontOptions = {} } = options;
    
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: ${fontOptions.defaultFont || 'Times New Roman, serif'};
            font-size: ${fontOptions.fontSize || 12}pt;
            line-height: 1.5;
            margin: 1in;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
  }

  /**
   * 便捷方法：直接从HTML字符串生成Word文件
   */
  async generateWordFile(
    htmlContent: string, 
    outputPath: string, 
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    const fs = require('fs').promises;
    
    try {
      const wordBuffer = await this.convertHtmlToWord(htmlContent, options);
      await fs.writeFile(outputPath, wordBuffer);
      console.log(`Word文档已生成: ${outputPath}`);
    } catch (error) {
      console.error('生成Word文件失败:', error);
      throw error;
    }
  }

  /**
   * 便捷方法：从HTML文件生成Word文件
   */
  async convertHtmlFileToWord(
    htmlFilePath: string, 
    outputPath: string, 
    options: HtmlToWordOptions = {}
  ): Promise<void> {
    const fs = require('fs').promises;
    
    try {
      const htmlContent = await fs.readFile(htmlFilePath, 'utf-8');
      await this.generateWordFile(htmlContent, outputPath, options);
    } catch (error) {
      console.error('从HTML文件转换失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const htmlToWordConverter = new SimpleHtmlToWordConverter();

// 导出便捷函数
export async function convertHtmlToWord(
  htmlContent: string, 
  options: HtmlToWordOptions = {}
): Promise<Buffer> {
  return htmlToWordConverter.convertHtmlToWord(htmlContent, options);
}

export async function generateWordFromHtml(
  htmlContent: string, 
  outputPath: string, 
  options: HtmlToWordOptions = {}
): Promise<void> {
  return htmlToWordConverter.generateWordFile(htmlContent, outputPath, options);
}
