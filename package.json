{"name": "my-midway-project", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/axios": "^3.20.4", "@midwayjs/bootstrap": "^3.12.0", "@midwayjs/bullmq": "^3.20.4", "@midwayjs/busboy": "^3.20.4", "@midwayjs/core": "^3.12.0", "@midwayjs/info": "^3.12.0", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/typeorm": "^3.20.4", "@midwayjs/validate": "^3.12.0", "async-retry": "^1.3.3", "axios": "^1.6.0", "docx": "^9.4.1", "docx4js": "^3.3.0", "html-docx-js": "^0.3.1", "html-to-docx": "^1.8.0", "jsdom": "^26.1.0", "jszip": "^3.10.1", "mammoth": "^1.9.0", "mathjax-node": "^2.1.1", "mathml-to-latex": "^1.4.3", "omml2mathml": "^1.3.0", "pizzip": "^3.1.8", "sharp": "^0.34.1", "xmldom": "^0.6.0"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/jest": "^29.2.0", "@types/jsdom": "^21.1.7", "@types/node": "14", "@types/sharp": "^0.32.0", "@types/xmldom": "^0.1.34", "cross-env": "^6.0.0", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.2", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}