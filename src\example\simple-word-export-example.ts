import { 
  SimpleHtmlToWordConverter, 
  convertHtmlToWord, 
  generateWordFromHtml,
  HtmlToWordOptions 
} from '../utils/word-export/simple-html-to-word';
import * as path from 'path';

async function testSimpleHtmlToWord() {
  console.log('=== 测试简化版HTML转Word功能 ===\n');

  // 测试HTML内容
  const testHtml = `
    <h1 style="text-align: center; color: #2c3e50;">试题导出测试</h1>
    
    <div style="margin: 20px 0;">
      <p><strong>【题型】</strong>单选题</p>
      <p><strong>【题干】</strong>以下哪个选项是正确的？</p>
      
      <div style="margin-left: 20px;">
        <p>A. 选项A的内容</p>
        <p>B. 选项B的内容</p>
        <p>C. 选项C的内容</p>
        <p>D. 选项D的内容</p>
      </div>
      
      <p><strong>【答案】</strong>C</p>
      <p><strong>【解析】</strong>这是详细的解析内容...</p>
    </div>

    <hr>

    <div style="margin: 20px 0;">
      <p><strong>【题型】</strong>多选题</p>
      <p><strong>【题干】</strong>以下哪些选项是正确的？</p>
      
      <div style="margin-left: 20px;">
        <p>A. 多选选项A</p>
        <p>B. 多选选项B</p>
        <p>C. 多选选项C</p>
        <p>D. 多选选项D</p>
      </div>
      
      <p><strong>【答案】</strong>AC</p>
      <p><strong>【解析】</strong>多选题的解析内容...</p>
    </div>

    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <tr>
        <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;">题目编号</th>
        <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;">题型</th>
        <th style="border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;">难度</th>
      </tr>
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">001</td>
        <td style="border: 1px solid #ddd; padding: 8px;">单选题</td>
        <td style="border: 1px solid #ddd; padding: 8px;">中等</td>
      </tr>
      <tr>
        <td style="border: 1px solid #ddd; padding: 8px;">002</td>
        <td style="border: 1px solid #ddd; padding: 8px;">多选题</td>
        <td style="border: 1px solid #ddd; padding: 8px;">困难</td>
      </tr>
    </table>
  `;

  const converter = new SimpleHtmlToWordConverter();
  const outputDir = path.join(__dirname, '../../testFiles');

  // 确保输出目录存在
  const fs = require('fs');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  try {
    // 测试1: 使用html-to-docx方法
    console.log('1. 测试html-to-docx方法...');
    const options1: HtmlToWordOptions = {
      method: 'html-to-docx',
      preprocessHtml: true,
      fontOptions: {
        defaultFont: 'Microsoft YaHei',
        fontSize: 12
      },
      pageOptions: {
        margins: {
          top: 1440, // 1英寸 = 1440 twips
          right: 1440,
          bottom: 1440,
          left: 1440
        }
      }
    };

    const outputPath1 = path.join(outputDir, 'test-html-to-docx.docx');
    await converter.generateWordFile(testHtml, outputPath1, options1);
    console.log(`✓ html-to-docx方法测试完成: ${outputPath1}\n`);

    // 测试2: 使用html-docx-js方法
    console.log('2. 测试html-docx-js方法...');
    const options2: HtmlToWordOptions = {
      method: 'html-docx-js',
      preprocessHtml: true,
      fontOptions: {
        defaultFont: 'Microsoft YaHei',
        fontSize: 12
      }
    };

    const outputPath2 = path.join(outputDir, 'test-html-docx-js.docx');
    await converter.generateWordFile(testHtml, outputPath2, options2);
    console.log(`✓ html-docx-js方法测试完成: ${outputPath2}\n`);

    // 测试3: 使用便捷函数
    console.log('3. 测试便捷函数...');
    const outputPath3 = path.join(outputDir, 'test-convenience-function.docx');
    await generateWordFromHtml(testHtml, outputPath3, {
      method: 'html-to-docx',
      preprocessHtml: true
    });
    console.log(`✓ 便捷函数测试完成: ${outputPath3}\n`);

    // 测试4: 直接获取Buffer
    console.log('4. 测试直接获取Buffer...');
    const wordBuffer = await convertHtmlToWord(testHtml, {
      method: 'html-to-docx',
      preprocessHtml: true
    });
    console.log(`✓ Buffer获取成功，大小: ${wordBuffer.length} bytes\n`);

    console.log('=== 所有测试完成 ===');
    console.log('生成的文件位置:');
    console.log(`- ${outputPath1}`);
    console.log(`- ${outputPath2}`);
    console.log(`- ${outputPath3}`);
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 测试复杂HTML内容
async function testComplexHtml() {
  console.log('\n=== 测试复杂HTML内容 ===\n');

  const complexHtml = `
    <div style="font-family: Microsoft YaHei;">
      <h2 style="color: #e74c3c; text-align: center;">数学试题</h2>
      
      <div style="background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff;">
        <p><span style="font-weight: bold; color: #007bff;">【题型】</span>计算题</p>
        <p><span style="font-weight: bold; color: #007bff;">【题干】</span>计算下列表达式的值：</p>
        
        <div style="text-align: center; margin: 20px 0; font-size: 16px;">
          <p><em>f(x) = x² + 2x + 1</em></p>
          <p>当 x = 3 时，求 f(x) 的值</p>
        </div>
        
        <p><span style="font-weight: bold; color: #28a745;">【答案】</span>16</p>
        
        <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px;">
          <p><span style="font-weight: bold; color: #856404;">【解析】</span></p>
          <p>将 x = 3 代入表达式：</p>
          <p>f(3) = 3² + 2×3 + 1 = 9 + 6 + 1 = 16</p>
        </div>
      </div>

      <div style="margin-top: 30px;">
        <h3>题目统计表</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background-color: #343a40; color: white;">
              <th style="border: 1px solid #dee2e6; padding: 12px;">序号</th>
              <th style="border: 1px solid #dee2e6; padding: 12px;">题型</th>
              <th style="border: 1px solid #dee2e6; padding: 12px;">难度</th>
              <th style="border: 1px solid #dee2e6; padding: 12px;">分值</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">1</td>
              <td style="border: 1px solid #dee2e6; padding: 8px;">单选题</td>
              <td style="border: 1px solid #dee2e6; padding: 8px; color: #28a745;">简单</td>
              <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">5分</td>
            </tr>
            <tr style="background-color: #f8f9fa;">
              <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">2</td>
              <td style="border: 1px solid #dee2e6; padding: 8px;">计算题</td>
              <td style="border: 1px solid #dee2e6; padding: 8px; color: #ffc107;">中等</td>
              <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">10分</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  `;

  const outputPath = path.join(__dirname, '../../testFiles/test-complex-html.docx');
  
  try {
    await generateWordFromHtml(complexHtml, outputPath, {
      method: 'html-to-docx',
      preprocessHtml: true,
      fontOptions: {
        defaultFont: 'Microsoft YaHei',
        fontSize: 11
      }
    });
    
    console.log(`✓ 复杂HTML测试完成: ${outputPath}`);
  } catch (error) {
    console.error('复杂HTML测试失败:', error);
  }
}

// 运行测试
async function runAllTests() {
  await testSimpleHtmlToWord();
  await testComplexHtml();
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

export { testSimpleHtmlToWord, testComplexHtml, runAllTests };
