import {
  HtmlToWordConverter,
  HtmlToWordOptions,
} from '../utils/word-export/html-to-word-converter';
import * as path from 'path';
import * as fs from 'fs';

// 输入输出文件路径
const testFilesDir = path.join(__dirname, '../../testFiles');

/**
 * 两阶段Word转换测试
 * 测试新的两阶段处理方案：html-to-docx + 着重号后处理
 */
async function testTwoStageWordConversion() {
  console.log('=== 两阶段Word转换测试 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 读取输入HTML
    const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');
    if (!fs.existsSync(inputHtmlPath)) {
      throw new Error(`输入HTML文件不存在: ${inputHtmlPath}`);
    }

    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');
    console.log(`输入文件: ${inputHtmlPath}`);
    console.log(`HTML内容长度: ${htmlContent.length.toLocaleString()} 字符`);

    // 转换配置
    const options: HtmlToWordOptions = {
      font: 'Microsoft YaHei',
      fontSize: 12,
      orientation: 'portrait',
      margins: {
        top: 1440,
        right: 1440,
        bottom: 1440,
        left: 1440
      },
      preprocessHtml: true  // 启用预处理
    };

    console.log('\n🚀 开始两阶段转换...');
    const startTime = Date.now();

    // 使用新的两阶段处理方法
    const outputPath = path.join(testFilesDir, 'two-stage-result.docx');
    await converter.generateWordFileWithEmphasis(htmlContent, outputPath, options);

    const endTime = Date.now();
    console.log(`✓ 两阶段转换完成！总耗时: ${endTime - startTime}ms`);

    // 检查输出文件
    const stats = await fs.promises.stat(outputPath);
    console.log(`✓ 输出文件: ${outputPath}`);
    console.log(`✓ 文件大小: ${Math.round(stats.size / 1024)}KB`);

    // 检查是否生成了着重号报告
    const reportPath = path.join(testFilesDir, 'emphasis-processing-report.json');
    if (fs.existsSync(reportPath)) {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf-8'));
      console.log(`✓ 着重号报告: ${reportPath}`);
      console.log(`  - 着重号数量: ${report.totalEmphasis}`);
      console.log(`  - 处理方法: ${report.processingMethod}`);
      console.log(`  - 阶段1: ${report.stage1}`);
      console.log(`  - 阶段2: ${report.stage2}`);
    }

    console.log('\n=== 两阶段转换测试完成 ===');
    console.log('📋 检查要点:');
    console.log('1. 打开 two-stage-result.docx 查看转换效果');
    console.log('2. 检查着重号是否显示为粗体下划线（阶段1效果）');
    console.log('3. 查看 emphasis-processing-report.json 了解处理详情');
    console.log('4. 对比之前的转换结果，看是否有改善');

  } catch (error) {
    console.error('❌ 两阶段转换测试失败:', error);
    throw error;
  }
}

/**
 * 对比测试：传统方法 vs 两阶段方法
 */
async function compareConversionMethods() {
  console.log('\n=== 转换方法对比测试 ===\n');

  const converter = new HtmlToWordConverter();

  try {
    // 读取输入HTML
    const inputHtmlPath = path.join(testFilesDir, 'input-html-sample.html');
    const htmlContent = await fs.promises.readFile(inputHtmlPath, 'utf-8');

    const options: HtmlToWordOptions = {
      font: 'Microsoft YaHei',
      fontSize: 12,
      preprocessHtml: true
    };

    console.log('🔄 方法1: 传统单阶段转换');
    const traditionalStart = Date.now();
    const traditionalPath = path.join(testFilesDir, 'traditional-method.docx');
    await converter.generateWordFile(htmlContent, traditionalPath, options);
    const traditionalTime = Date.now() - traditionalStart;
    const traditionalStats = await fs.promises.stat(traditionalPath);
    console.log(`✓ 传统方法完成: ${traditionalTime}ms, ${Math.round(traditionalStats.size / 1024)}KB`);

    console.log('\n🔄 方法2: 两阶段处理转换');
    const twoStageStart = Date.now();
    const twoStagePath = path.join(testFilesDir, 'two-stage-method.docx');
    await converter.generateWordFileWithEmphasis(htmlContent, twoStagePath, options);
    const twoStageTime = Date.now() - twoStageStart;
    const twoStageStats = await fs.promises.stat(twoStagePath);
    console.log(`✓ 两阶段方法完成: ${twoStageTime}ms, ${Math.round(twoStageStats.size / 1024)}KB`);

    console.log('\n📊 对比结果:');
    console.log(`- 传统方法: ${traditionalTime}ms, ${Math.round(traditionalStats.size / 1024)}KB`);
    console.log(`- 两阶段方法: ${twoStageTime}ms, ${Math.round(twoStageStats.size / 1024)}KB`);
    console.log(`- 时间差异: ${twoStageTime - traditionalTime}ms (${twoStageTime > traditionalTime ? '较慢' : '较快'})`);
    console.log(`- 大小差异: ${Math.round(twoStageStats.size / 1024) - Math.round(traditionalStats.size / 1024)}KB`);

    console.log('\n📋 下一步:');
    console.log('1. 打开两个Word文档对比效果');
    console.log('2. 检查着重号显示是否有差异');
    console.log('3. 验证内容完整性');

  } catch (error) {
    console.error('❌ 对比测试失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 测试1: 两阶段转换
    await testTwoStageWordConversion();
    
    // 测试2: 方法对比
    await compareConversionMethods();
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

export { testTwoStageWordConversion, compareConversionMethods, main };
